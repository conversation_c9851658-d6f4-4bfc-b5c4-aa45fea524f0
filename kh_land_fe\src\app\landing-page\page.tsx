'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js'; // ✅ Đảm bảo dropdown + collapse hoạt động
import Carousel from './carousel';
import Footer from './footer';
import Section_1 from './section-1';
import Section_2 from './section-2';
import Section_0 from './section-0';
import Landing from './landing';
import Header from './header';
import Cover from './cover';
import OverlayRegister from './card-blur';

export default function LandingPage() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false); // ⬅️ Replace x-data
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      router.push('/landing-page');
    }
  }, []);

  return (
    <>
  < Header />
{/*Landing picture*/}
< Landing />
{/* hero section */}
< Section_0 />
{/* t<PERSON>h năng nổi bật*/}
< Section_1 />
{/* <PERSON><PERSON>ch thức hoạt động */}
< Section_2 />
< Carousel />
< Cover />
< OverlayRegister />

< Footer />
</>
  );
}
