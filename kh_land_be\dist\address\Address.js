"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Address = void 0;
const typeorm_1 = require("typeorm");
const Wards_1 = require("./wards/Wards");
const Home_1 = require("../home/<USER>");
const News_1 = require("../news/News");
let Address = class Address {
};
exports.Address = Address;
__decorate([
    (0, typeorm_1.Column)("uuid", {
        primary: true,
        name: "address_id",
        default: () => "gen_random_uuid()",
    }),
    __metadata("design:type", String)
], Address.prototype, "addressId", void 0);
__decorate([
    (0, typeorm_1.Column)("numeric", { name: "latitude", nullable: true }),
    __metadata("design:type", Object)
], Address.prototype, "latitude", void 0);
__decorate([
    (0, typeorm_1.Column)("numeric", { name: "longitude", nullable: true }),
    __metadata("design:type", Object)
], Address.prototype, "longitude", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "street", nullable: true }),
    __metadata("design:type", Object)
], Address.prototype, "street", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "ward_id", nullable: true }),
    __metadata("design:type", Object)
], Address.prototype, "wardId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Wards_1.Wards, (wards) => wards.addresses, {
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)([{ name: "ward_id", referencedColumnName: "wardId" }]),
    __metadata("design:type", Wards_1.Wards)
], Address.prototype, "ward", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Home_1.Home, (home) => home.address),
    __metadata("design:type", Array)
], Address.prototype, "homes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => News_1.News, (news) => news.address),
    __metadata("design:type", Array)
], Address.prototype, "news", void 0);
exports.Address = Address = __decorate([
    (0, typeorm_1.Index)("idx_address_ward_id", ["wardId"], {}),
    (0, typeorm_1.Entity)("address", { schema: "public" })
], Address);
//# sourceMappingURL=Address.js.map