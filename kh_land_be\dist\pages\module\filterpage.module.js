"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterPageModule = void 0;
const common_1 = require("@nestjs/common");
const home_module_1 = require("../../home/<USER>");
const filterpage_controller_1 = require("../controller/filterpage.controller");
let FilterPageModule = class FilterPageModule {
};
exports.FilterPageModule = FilterPageModule;
exports.FilterPageModule = FilterPageModule = __decorate([
    (0, common_1.Module)({
        imports: [home_module_1.HomeModule],
        controllers: [filterpage_controller_1.FilterPageController],
    })
], FilterPageModule);
//# sourceMappingURL=filterpage.module.js.map