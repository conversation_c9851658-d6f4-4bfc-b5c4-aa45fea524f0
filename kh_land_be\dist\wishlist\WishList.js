"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WishList = void 0;
const typeorm_1 = require("typeorm");
const Users_1 = require("../users/Users");
let WishList = class WishList {
};
exports.WishList = WishList;
__decorate([
    (0, typeorm_1.Column)("uuid", { primary: true, name: "home_id" }),
    __metadata("design:type", String)
], WishList.prototype, "homeId", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { primary: true, name: "user_id" }),
    __metadata("design:type", String)
], WishList.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Users_1.Users, (users) => users.wishLists),
    (0, typeorm_1.JoinColumn)([{ name: "user_id", referencedColumnName: "userId" }]),
    __metadata("design:type", Users_1.Users)
], WishList.prototype, "user", void 0);
exports.WishList = WishList = __decorate([
    (0, typeorm_1.Index)("idx_wish_list_home_id", ["homeId"], {}),
    (0, typeorm_1.Index)("idx_wish_list_user_id", ["userId"], {}),
    (0, typeorm_1.Entity)("wish_list", { schema: "public" })
], WishList);
//# sourceMappingURL=WishList.js.map