import { validate } from 'class-validator';
import { LoginDto } from './login.dto';

describe('LoginDto', () => {
  it('should be valid when identifier and password are provided', async () => {
    const dto = new LoginDto();
    dto.identifier = '<EMAIL>';
    dto.password = 'password123';
    const errors = await validate(dto);

    expect(errors.length).toBe(0);
  });

  it('should be invalid if identifier is missing', async () => {
    const dto = new LoginDto();
    dto.password = 'password123';
    const errors = await validate(dto);

    expect(errors.some((e) => e.property === 'identifier')).toBe(true);
  });

  it('should be invalid if password is missing', async () => {
    const dto = new LoginDto();
    dto.identifier = '<EMAIL>';
    const errors = await validate(dto);

    expect(errors.some((e) => e.property === 'password')).toBe(true);
  });

  it('should be invalid if identifier is empty', async () => {
    const dto = new LoginDto();
    dto.identifier = '';
    dto.password = 'password123';
    const errors = await validate(dto);

    expect(errors.some((e) => e.property === 'identifier')).toBe(true);
  });

  it('should be invalid if password is empty', async () => {
    const dto = new LoginDto();
    dto.identifier = '<EMAIL>';
    dto.password = '';
    const errors = await validate(dto);

    expect(errors.some((e) => e.property === 'password')).toBe(true);
  });

  it('should be invalid if identifier is not a string', async () => {
    const dto = new LoginDto();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    dto.identifier = 12345;
    dto.password = 'password123';
    const errors = await validate(dto);

    expect(errors.some((e) => e.property === 'identifier')).toBe(true);
  });

  it('should be invalid if password is not a string', async () => {
    const dto = new LoginDto();
    dto.identifier = '<EMAIL>';
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    dto.password = 12345;
    const errors = await validate(dto);

    expect(errors.some((e) => e.property === 'password')).toBe(true);
  });
});
