"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const News_1 = require("./News");
const address_service_1 = require("../address/address.service");
let NewsService = class NewsService {
    constructor(newsRepository, addressService) {
        this.newsRepository = newsRepository;
        this.addressService = addressService;
    }
    async createNews(newsData) {
        const newNews = this.newsRepository.create(newsData);
        return this.newsRepository.save(newNews);
    }
    async getAllNews() {
        const homes = await this.newsRepository.find({
            where: { newsStatus: 'ACTIVATE' },
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const home of homes) {
            home.address = await this.addressService.getAddressById(home.address.addressId);
        }
        return homes;
    }
    async getNewsById(newsId) {
        const news = await this.newsRepository.findOne({
            where: { newsId },
            relations: ['author', 'address', 'newsPhotos'],
        });
        if (!news)
            return null;
        news.address = await this.addressService.getAddressById(news.address.addressId);
        return news;
    }
    async updateNewsById(newsId, updateNewsInfo) {
        await this.newsRepository.update({ newsId }, updateNewsInfo);
        return this.newsRepository.findOne({
            where: { newsId },
            relations: ['author', 'address', 'newsPhotos'],
        });
    }
    async deleteNewsById(newsId) {
        const news = await this.getNewsById(newsId);
        if (!news)
            return null;
        await this.newsRepository.delete({ newsId });
        return news;
    }
    async getLatestNews(limit = 10) {
        const news = await this.newsRepository.find({
            where: { newsStatus: 'ACTIVATE' },
            order: { createdAt: 'DESC' },
            take: limit,
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const n of news) {
            n.address = await this.addressService.getAddressById(n.address.addressId);
        }
        return news;
    }
    async getMostViewedNews(limit = 10) {
        const news = await this.newsRepository.find({
            order: { views: 'DESC' },
            where: { newsStatus: 'ACTIVATE' },
            take: limit,
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const n of news) {
            n.address = await this.addressService.getAddressById(n.address.addressId);
        }
        return news;
    }
    async getRecommendedNews(limit = 10) {
        const news = await this.newsRepository.find({
            where: { newsStatus: 'ACTIVATE' },
            order: { priority: 'DESC' },
            take: limit,
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const n of news) {
            n.address = await this.addressService.getAddressById(n.address.addressId);
        }
        return news;
    }
};
exports.NewsService = NewsService;
exports.NewsService = NewsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(News_1.News)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        address_service_1.AddressService])
], NewsService);
//# sourceMappingURL=news.service.js.map