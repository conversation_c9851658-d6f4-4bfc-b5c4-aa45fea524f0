// src/utils/zod.utils.ts
import { ZodError } from 'zod';
import { ERROR_MESSAGES } from './error.messages';

export function formatZodErrors(err: ZodError) {
    return err.errors.map(e => {
        const err_code = e.message;
        const message = ERROR_MESSAGES[err_code] || 'Invalid input';

        return {
            field: e.path.join('.') || 'unknown',
            message,
            err_code,
        };
    });
}
