// 'use client';
// import { useParams } from 'next/navigation';
// import React, { useEffect, useState } from 'react';
// import 'bootstrap/dist/css/bootstrap.min.css';
// import axios from 'axios';
// interface Country {
//   countryId: number;
//   countryName: string;
// }

// interface City {
//   cityId: number;
//   cityName: string;
//   countryId: number;
//   country: Country;
// }

// interface District {
//   districtId: number;
//   districtName: string;
//   cityId: number;
//   city: City;
// }

// interface Ward {
//   wardId: number;
//   wardName: string;
//   districtId: number;
//   district: District;
// }

// interface Address {
//   addressId: string;
//   latitude: string;
//   longitude: string;
//   street: string;
//   wardId: number;
//   ward: Ward;
// }

// interface Seller {
//   userId: string;
//   firstName: string;
//   lastName: string;
// }

// interface HomeType {
//   homeTypeName: string;
// }

// interface HomePhoto {
//   photoUrl: string;
// }

// interface Amenity {
//   amenityName: string;
// }

// interface Home {
//   homeId: string;
//   price: string;
//   squareFeet: number;
//   yearBuilt: number;
//   bathroom: number;
//   bedroom: number;
//   homeName: string;
//   views: number;
//   homeDescription: string;
//   areaInformation: string;
//   homeType: HomeType;
//   homePhotos: HomePhoto[];
//   amenities: Amenity[];
//   address: Address;
//   seller: Seller;
// }

// // const params = useParams();
// // const homeId = "1e25459e-2899-48db-b88c-3a2f119b135e";
// const viewed = JSON.parse(localStorage.getItem("viewedHomes") || "[]");
// export default function PropertyDetailPage() {
//   const params = useParams();
//   const homeId = params.homeId as string;
//   const [home, setHome] = useState<Home | null>(null);
//   useEffect(() => {
//     console.log("homeId:", viewed);
//     const fetchHome = async () => {
//       try {
//         const res = await axios.get(`http://localhost:3001/home/<USER>/${homeId}`);
//         setHome(res.data);
//       } catch (error) {
//         console.error('Lỗi khi fetch dữ liệu:', error);
//       }
//     };
//     fetchHome();
//     viewIncrease();
//   }, [homeId]);
//   async function viewIncrease() {
//     if (viewed.includes(homeId)) return;
//     const timeoutId = setTimeout(() => {
//       axios.post(`http://localhost:3001/home/<USER>/increase-view`)
//         .then(() => {
//           const updatedViewed = [...viewed, homeId];
//           localStorage.setItem("viewedHomes", JSON.stringify(updatedViewed));
//         })
//         .catch((error) => {
//           alert("❌ Tăng lượt xem thất bại!");
//         });
//     }, 5 * 60 * 1000);

//     return () => clearTimeout(timeoutId);
//   }

//   if (!home) return null;
//   return (
//     <div className="container py-4">
//       {/* Ảnh */}
//       <div className="mb-4">
//         {home.homePhotos.map((photo, i) => (
//           <div key={i} >
//             <img
//               src={photo.photoUrl}
//               alt={`Ảnh ${i + 1}`}
//               className="img-fluid rounded"
//               style={{ objectFit: 'cover', height: '200px', width: '100%' }}
//             />
//           </div>
//         ))}
//       </div>


//       {/* Thân trang */}
//       <div className="row">
//         {/* Thông tin bất động sản */}
//         <div className="col-md-8">
//           <div className="border rounded p-4 mb-4">
//             <h3 className="fw-bold">{home.homeName}</h3>
//             <p className="text-muted">{home.address.ward.wardName} {home.address.ward.district.districtName}</p>
//             <h4 className="text-primary fw-bold">{home.price}</h4>

//             <div className="d-flex flex-wrap gap-3 mb-4">
//               <span>🛏 {home.bedroom} phòng ngủ</span>
//               <span>🚿 {home.bathroom} phòng tắm</span>
//               <span>📐 {home.squareFeet} m²</span>
//               <span>📅 Năm xây: {home.yearBuilt}</span>
//             </div>
//           </div>
//           <div className="mb-4">
//             <div className="border rounded p-4 mb-4">
//               <h5 className="fw-semibold mb-2">Mô tả chi tiết</h5>
//               <p className="text-secondary">{home.homeDescription}</p>
//             </div>
//           </div>

//           <div className="mb-4">
//             <div className="border rounded p-4 mb-4">
//               <h5 className="fw-semibold mb-2">Tiện ích</h5>
//               <div className="d-flex flex-wrap gap-2">
//                 {home.amenities.map((a, i) => (
//                   <span key={i} className="badge bg-light text-dark border">
//                     {a.amenityName}
//                   </span>
//                 ))}
//               </div>
//             </div>
//           </div>

//           {/* ✅ Thông tin khu vực bo góc + mô tả văn bản */}
//           <div className="mb-4 border rounded p-4">
//             <h5 className="fw-semibold mb-2">Thông tin khu vực</h5>
//             <p className="text-secondary mb-3">
//               {home.areaInformation}
//             </p>
//             <div style={{ borderRadius: '8px', overflow: 'hidden' }}>
//               <iframe
//                 src={`https://maps.google.com/maps?q=${encodeURIComponent(`${home.address.street}, ${home.address.ward.wardName}, ${home.address.ward.district.districtName}`)}&z=15&output=embed`}
//                 width="100%"
//                 height="300"
//                 frameBorder="0"
//                 loading="lazy"
//                 style={{ border: 0 }}
//               ></iframe>
//             </div>
//           </div>
//         </div>
//         {/* Form liên hệ */}
//         <div className="col-md-4">
//           <div className="card shadow-sm p-4">
//             <h5 className="fw-semibold mb-3">Liên hệ người bán</h5>
//             <form>
//               <div className="mb-3">
//                 <input type="text" className="form-control" placeholder="Họ và tên" />
//               </div>
//               <div className="mb-3">
//                 <input type="email" className="form-control" placeholder="Email" />
//               </div>
//               <div className="mb-3">
//                 <input type="tel" className="form-control" placeholder="Số điện thoại" />
//               </div>
//               <div className="mb-3">
//                 <textarea className="form-control" rows={3} placeholder="Tin nhắn của bạn..." />
//               </div>
//               <button type="submit" className="btn btn-primary w-100">
//                 Liên hệ
//               </button>
//             </form>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }


'use client';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';
interface Country {
  countryId: number;
  countryName: string;
}

interface City {
  cityId: number;
  cityName: string;
  countryId: number;
  country: Country;
}

interface District {
  districtId: number;
  districtName: string;
  cityId: number;
  city: City;
}

interface Ward {
  wardId: number;
  wardName: string;
  districtId: number;
  district: District;
}

interface Address {
  addressId: string;
  latitude: string;
  longitude: string;
  street: string;
  wardId: number;
  ward: Ward;
}

interface Seller {
  userId: string;
  firstName: string;
  lastName: string;
}

interface HomeType {
  homeTypeName: string;
}

interface HomePhoto {
  photoUrl: string;
}

interface Amenity {
  amenityName: string;
}

interface Home {
  homeId: string;
  price: string;
  squareFeet: number;
  yearBuilt: number;
  bathroom: number;
  bedroom: number;
  homeName: string;
  views: number;
  homeDescription: string;
  areaInformation: string;
  homeType: HomeType;
  homePhotos: HomePhoto[];
  amenities: Amenity[];
  address: Address;
  seller: Seller;
}

// const params = useParams();
// const homeId = "1e25459e-2899-48db-b88c-3a2f119b135e";
const viewed = JSON.parse(localStorage.getItem("viewedHomes") || "[]");
export default function PropertyDetailPage() {
  const params = useParams();
  const homeId = params.homeId as string;
  const [home, setHome] = useState<Home | null>(null);
  useEffect(() => {
    console.log("homeId:", viewed);
    const fetchHome = async () => {
      try {
        const res = await axios.get(`http://localhost:3001/home/<USER>/${homeId}`);
        setHome(res.data);
      } catch (error) {
        console.error('Lỗi khi fetch dữ liệu:', error);
      }
    };
    fetchHome();
    viewIncrease();
  }, [homeId]);
  async function viewIncrease() {
    if (viewed.includes(homeId)) return;
    const timeoutId = setTimeout(() => {
      axios.post(`http://localhost:3001/home/<USER>/increase-view`)
        .then(() => {
          const updatedViewed = [...viewed, homeId];
          localStorage.setItem("viewedHomes", JSON.stringify(updatedViewed));
        })
        .catch((error) => {
          alert("❌ Tăng lượt xem thất bại!");
        });
    }, 5 * 60 * 1000);

    return () => clearTimeout(timeoutId);
  }

  if (!home) return null;
  return (
    <div className="container py-4">
      {/* Ảnh */}
      <div id="photoCarousel" className="carousel slide mb-4" data-bs-ride="carousel">
  <div className="carousel-inner">
    {home.homePhotos.map((photo, i) => (
      <div key={i} className={`carousel-item ${i === 0 ? 'active' : ''}`}>
        <img
          src={photo.photoUrl}
          alt={`Ảnh ${i + 1}`}
          className="d-block w-100 rounded"
          style={{ objectFit: 'cover', height: '400px' }}
        />
      </div>
    ))}
  </div>

  {home.homePhotos.length > 1 && (
    <>
      <button
        className="carousel-control-prev"
        type="button"
        data-bs-target="#photoCarousel"
        data-bs-slide="prev"
      >
        <span className="carousel-control-prev-icon" aria-hidden="true"></span>
        <span className="visually-hidden">Previous</span>
      </button>
      <button
        className="carousel-control-next"
        type="button"
        data-bs-target="#photoCarousel"
        data-bs-slide="next"
      >
        <span className="carousel-control-next-icon" aria-hidden="true"></span>
        <span className="visually-hidden">Next</span>
      </button>
    </>
  )}
</div>
      {/* Thân trang */}
      <div className="row">
        {/* Thông tin bất động sản */}
        <div className="col-md-8">
          <div className="border rounded p-4 mb-4">
            <h3 className="fw-bold">{home.homeName}</h3>
            <p className="text-muted">{home.address.ward.wardName} {home.address.ward.district.districtName}</p>
            <h4 className="text-primary fw-bold">{home.price}</h4>

            <div className="d-flex flex-wrap gap-3 mb-4">
              <span>🛏 {home.bedroom} phòng ngủ</span>
              <span>🚿 {home.bathroom} phòng tắm</span>
              <span>📐 {home.squareFeet} m²</span>
              <span>📅 Năm xây: {home.yearBuilt}</span>
            </div>
          </div>
          <div className="mb-4">
            <div className="border rounded p-4 mb-4">
              <h5 className="fw-semibold mb-2">Mô tả chi tiết</h5>
              <p className="text-secondary">{home.homeDescription}</p>
            </div>
          </div>

          <div className="mb-4">
            <div className="border rounded p-4 mb-4">
              <h5 className="fw-semibold mb-2">Tiện ích</h5>
              <div className="d-flex flex-wrap gap-2">
                {home.amenities.map((a, i) => (
                  <span key={i} className="badge bg-light text-dark border">
                    {a.amenityName}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* ✅ Thông tin khu vực bo góc + mô tả văn bản */}
          <div className="mb-4 border rounded p-4">
            <h5 className="fw-semibold mb-2">Thông tin khu vực</h5>
            <p className="text-secondary mb-3">
              {home.areaInformation}
            </p>
            <div style={{ borderRadius: '8px', overflow: 'hidden' }}>
              <iframe
                src={`https://maps.google.com/maps?q=${encodeURIComponent(`${home.address.street}, ${home.address.ward.wardName}, ${home.address.ward.district.districtName}`)}&z=15&output=embed`}
                width="100%"
                height="300"
                frameBorder="0"
                loading="lazy"
                style={{ border: 0 }}
              ></iframe>
            </div>
          </div>
        </div>
        {/* Form liên hệ */}
        <div className="col-md-4">
          <div className="card shadow-sm p-4">
            <h5 className="fw-semibold mb-3">Liên hệ người bán</h5>
            <form>
              <div className="mb-3">
                <input type="text" className="form-control" placeholder="Họ và tên" />
              </div>
              <div className="mb-3">
                <input type="email" className="form-control" placeholder="Email" />
              </div>
              <div className="mb-3">
                <input type="tel" className="form-control" placeholder="Số điện thoại" />
              </div>
              <div className="mb-3">
                <textarea className="form-control" rows={3} placeholder="Tin nhắn của bạn..." />
              </div>
              <button type="submit" className="btn btn-primary w-100">
                Liên hệ
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}