"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeTypeModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const HomeType_1 = require("./HomeType");
const homeType_controller_1 = require("./homeType.controller");
const homeType_service_1 = require("./homeType.service");
let HomeTypeModule = class HomeTypeModule {
};
exports.HomeTypeModule = HomeTypeModule;
exports.HomeTypeModule = HomeTypeModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([HomeType_1.HomeType])],
        controllers: [homeType_controller_1.HomeTypeController],
        providers: [homeType_service_1.HomeTypeService],
        exports: [homeType_service_1.HomeTypeService],
    })
], HomeTypeModule);
//# sourceMappingURL=homeType.module.js.map