'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { resetPassword, api } from '@/app/api/auth/service/authApi';
import React, { useEffect, useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import login from '@/styles/login.module.css';
import { Button } from 'react-bootstrap';
import NotifyModal from '@/components/NotifyModal';
import axios from 'axios';
import { ERROR_MESSAGES } from '@/utils/error.messages';

export default function ResetPasswordPage() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const token = searchParams.get('token');

    const [modalState, setModalState] = useState<{
        show: boolean;
        message: string;
        onClose?: () => void;
    }>({
        show: false,
        message: '',
    });

    const openModal = (message: string, onClose?: () => void) => {
        setModalState({ show: true, message, onClose });
    };

    const handleCloseModal = () => {
        setModalState((prev) => {
            const onClose = prev.onClose;
            setTimeout(() => {
                if (onClose) onClose();
            }, 0);
            return { show: false, message: '' };
        });
    };

    useEffect(() => {
        const localToken = localStorage.getItem('token');
        if (localToken) {
            router.push('/home');
            return;
        }

        if (!token) {
            openModal('Thiếu token trong URL.', () => router.push('/login'));
            return;
        }

        api.get('/auth/reset-password', { params: { token } })

            .then((res) => {
                if (!res.data.valid) {
                    openModal(res.data.message, () => router.push('/login'));
                }
            })
            .catch(() => {
                openModal('Token không hợp lệ hoặc đã hết hạn.', () => router.push('/login'));
            });
    }, [token, router]);

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const newPassword = formData.get('password') as string;
        const rePassword = formData.get('rePassword') as string;

        if (newPassword !== rePassword) {
            openModal('Mật khẩu và xác thực mật khẩu không khớp');
            return;
        }

        try {
            await resetPassword({ newPassword, token });
            openModal('Đặt lại mật khẩu thành công! Vui lòng đăng nhập lại.', () => {
                router.push('/login');
            });
        } catch (err: any) {
            let errorMsg = 'Đã xảy ra lỗi khi gửi yêu cầu đặt lại mật khẩu';

            if (axios.isAxiosError(err)) {
                const data = err.response?.data;
                const issues = Array.isArray(data?.issues)
                    ? data.issues
                    : Array.isArray(data?.message)
                        ? data.message
                        : null;

                if (Array.isArray(issues)) {
                    const messages = issues.map((issue: any) => {
                        const err_code = issue?.message || (typeof issue === 'string' ? issue : '');
                        return ERROR_MESSAGES[err_code] || err_code || 'Lỗi không xác định';
                    });
                    errorMsg = messages.join('<br />');
                } else if (typeof data?.message === 'string') {
                    const err_code = data.message;
                    errorMsg = ERROR_MESSAGES[err_code] || err_code;
                } else if (typeof data?.message === 'object' && data?.message !== null) {
                    const err_code = (data.message as any)?.err_code;
                    errorMsg = ERROR_MESSAGES[err_code] || err_code || JSON.stringify(data.message);
                }
            }

            openModal('Lỗi:<br />' + errorMsg);
        }
    };

    return (
        <div className={`${login.background} ${login.fullHeight}`}>
            <div className={login.container}>
                <div className={`row g-0 ${login.fullHeight} ${login.row}`}>
                    <div className={`col-md-6 d-none d-xl-block ${login.fullHeight} ${login.containerLeft}`}>
                        <img src="/img/login-landmark.jpg" alt="Photo" />
                    </div>
                    <div className={`col-12 col-xl-6 ${login.fullHeight} ${login.containerRight}`}>
                        <div className={login.avatarLogo}><p>KH</p></div>
                        <p className={login.title}>Chào mừng đến với Nền Tảng<br />Bất Động Sản!</p>
                        <p className={login.subtitle}>Hãy đăng nhập để bắt đầu.</p>

                        <form className={`${login.inputForm}`} onSubmit={handleSubmit}>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="password">Mật khẩu</label>
                                <input type="password" id="password" name="password" placeholder="Nhập Mật Khẩu" required />
                            </div>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="rePassword">Xác nhận mật khẩu</label>
                                <input type="password" id="rePassword" name="rePassword" placeholder="Nhập Lại Mật Khẩu" required />
                            </div>
                            <div>
                                <Button className={`${login.registerBtn} mt-5`} type="submit">
                                    Tạo mật khẩu mới
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <NotifyModal show={modalState.show} message={modalState.message} onClose={handleCloseModal} />
        </div>
    );
}
