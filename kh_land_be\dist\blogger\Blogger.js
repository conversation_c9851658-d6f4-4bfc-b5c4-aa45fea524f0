"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Blogger = void 0;
const typeorm_1 = require("typeorm");
const News_1 = require("../news/News");
const Users_1 = require("../users/Users");
let Blogger = class Blogger {
};
exports.Blogger = Blogger;
__decorate([
    (0, typeorm_1.Column)("uuid", { primary: true, name: "blogger_id" }),
    __metadata("design:type", String)
], Blogger.prototype, "bloggerId", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "blog_id", nullable: true }),
    __metadata("design:type", Object)
], Blogger.prototype, "blogId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => News_1.News, (news) => news.bloggers, { onDelete: "CASCADE" }),
    (0, typeorm_1.JoinColumn)([{ name: "blog_id", referencedColumnName: "newsId" }]),
    __metadata("design:type", News_1.News)
], Blogger.prototype, "blog", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => Users_1.Users, (users) => users.blogger, { onDelete: "CASCADE" }),
    (0, typeorm_1.JoinColumn)([{ name: "blogger_id", referencedColumnName: "userId" }]),
    __metadata("design:type", Users_1.Users)
], Blogger.prototype, "blogger", void 0);
exports.Blogger = Blogger = __decorate([
    (0, typeorm_1.Index)("idx_blogger_blog_id", ["blogId"], {}),
    (0, typeorm_1.Entity)("blogger", { schema: "public" })
], Blogger);
//# sourceMappingURL=Blogger.js.map