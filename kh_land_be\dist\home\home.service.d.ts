import { AddressService } from './../address/address.service';
import { Home } from './Home';
import { Repository, DataSource } from 'typeorm';
import { HomeDto } from './dto/home.dto';
import { FilterPageDto } from 'src/pages/dto/filter.dto';
export declare class HomeService {
    private readonly homeRepository;
    private readonly dataSource;
    private readonly addressService;
    constructor(homeRepository: Repository<Home>, dataSource: DataSource, addressService: AddressService);
    getAllHomes(): Promise<Home[]>;
    getHomeById(homeId: string): Promise<Home | null>;
    createHome(homeData: HomeDto): Promise<Home>;
    updateHomeById(homeId: string, updateHomeInfo: Partial<Home>): Promise<void>;
    deleteHomeById(homeId: string): Promise<Home | null>;
    getMostViewedHomes(limit?: number): Promise<Home[]>;
    getLastAddedHomes(limit?: number): Promise<Home[]>;
    getRecommendedHomes(limit?: number): Promise<Home[]>;
    getHostPropertiesHome(limit?: number): Promise<Home[]>;
    getListHomeOrderBy(filter: keyof Home, limit?: number): Promise<Home[]>;
    searchByFilter(filters: FilterPageDto, pageSize?: number): Promise<{
        homeId: string;
        homeName: string | null;
        price: string | null;
        squareFeet: number | null;
        homeType: {
            homeTypeName: string | null;
        };
        amenities: {
            amenityName: string;
        }[];
        homeDescription: string | null;
        districtName: string | null;
        cityName: string | null;
    }[]>;
}
