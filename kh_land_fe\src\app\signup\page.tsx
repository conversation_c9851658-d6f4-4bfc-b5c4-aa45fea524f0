'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { Button } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css'
import login from '@/styles/login.module.css'
import NotifyModal from '@/components/NotifyModal';
import { registerUser, redirectToGoogleLogin, redirectToFacebookLogin } from '@/app/api/auth/service/authApi';
import Link from 'next/link';
import axios from 'axios';
import { ERROR_MESSAGES } from '@/utils/error.messages';

export default function RegisterPage() {
    const router = useRouter();

    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            router.push('/home');
        }
    }, []);

    const [passwordError, setPasswordError] = useState('');
    const [modalState, setModalState] = useState<{
        show: boolean;
        message: string;
        onClose?: () => void;
    }>({
        show: false,
        message: '',
    })

    const openModal = (message: string, onClose?: () => void) => {
        setModalState({ show: true, message, onClose });
    }

    const handleCloseModal = () => {
        setModalState((prev) => {
            const onClose = prev.onClose;
            setTimeout(() => {
                if (onClose) onClose();
            }, 0);
            return { show: false, message: '' };
        });
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        const formData = new FormData(e.currentTarget);
        const firstName = formData.get('firstName') as string;
        const lastName = formData.get('lastName') as string;
        const email = formData.get('email') as string;
        const phoneNumber = formData.get('phoneNumber') as string;
        const password = formData.get('password') as string;
        const rePassword = formData.get('rePassword') as string;

        if (password !== rePassword) {
            setPasswordError('Mật khẩu và xác thực mật khẩu không khớp');
            return;
        }
        setPasswordError('');
        try {
            await registerUser({ firstName, lastName, email, phoneNumber, password });

            openModal('Đăng ký thành công! Vui lòng đăng nhập.', () => {
                router.push('/login');
            });

        } catch (err: any) {
            let errorMsg = 'Đã xảy ra lỗi khi đăng ký tài khoản';
            if (axios.isAxiosError(err)) {
                const data = err.response?.data;
                const issues = Array.isArray(data?.issues)
                    ? data.issues
                    : Array.isArray(data?.message)
                        ? data.message
                        : null;

                if (Array.isArray(issues)) {
                    const messages = issues.map((issue: any) => {
                        const err_code = issue?.message || (typeof issue === 'string' ? issue : '');
                        return ERROR_MESSAGES[err_code] || err_code || 'Lỗi không xác định';
                    });
                    errorMsg = messages.join('<br />');
                } else if (typeof data?.message === 'string') {
                    const err_code = data.message;
                    errorMsg = ERROR_MESSAGES[err_code] || err_code;
                } else if (typeof data?.message === 'object' && data?.message !== null) {
                    const err_code = (data.message as any)?.err_code;
                    errorMsg = ERROR_MESSAGES[err_code] || err_code || JSON.stringify(data.message);
                }
            }

            openModal('Reset password failed:<br />' + errorMsg);

        }

    }
    return (
        <div className={`${login.background} ${login.fullHeight}`}>
            <div className={login.container}>
                <div className={`row g-0 ${login.fullHeight} ${login.row}`}>
                    <div className={`col-md-6 d-none d-xl-block ${login.fullHeight} ${login.containerLeft}`}>
                        <img src="/img/login-landmark.jpg" alt="Photo" />
                    </div>
                    <div className={`col-12 col-xl-6 ${login.fullHeight} ${login.containerRight}`}>
                        <div className={login.avatarLogo}>
                            <p>
                                KH
                            </p>
                        </div>
                        <p className={login.title}>
                            Chào mừng đến với Nền Tảng<br />
                            Bất Động Sản!
                        </p>
                        <p className={login.subtitle}>
                            Hãy tạo tài khoản của bạn để bắt đầu.
                        </p>
                        <form className={`${login.inputForm}`} onSubmit={handleSubmit}>
                            <div className="row g-2 ">
                                <div className={`col-12 col-xl-6 text-start ${login.nameInput}`}>
                                    <label htmlFor="firstName">
                                        Họ
                                    </label>
                                    <br />
                                    <input type="text" id="firstName" name='firstName' placeholder='Nhập họ của bạn' required />
                                </div>
                                <div className={`col-12 col-xl-6 text-xl-end ${login.nameInput}`}>
                                    <label htmlFor="lastName" style={{ marginRight: 160 }}>
                                        Tên
                                    </label>
                                    <br />
                                    <input type="text" id="lastName" name='lastName' placeholder='Nhập tên của bạn' required />
                                </div>
                            </div>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="email">
                                    Email
                                </label>
                                <input type="text" id="email" name='email' placeholder='Nhập email của bạn' required />
                            </div>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="phoneNumber">
                                    Số điện thoại
                                </label>
                                <input type="text" id="phoneNumber" name='phoneNumber' placeholder='Nhập số điện thoại của bạn' required />
                            </div>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="password">
                                    Mật khẩu
                                </label>
                                <input type="password" id="password" name='password' placeholder='Nhập Mật Khẩu' required />
                            </div>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="rePassword">
                                    Xác nhận mật khẩu
                                </label>
                                <input type="password" id="rePassword" name='rePassword' placeholder='Nhập Lại Mật Khẩu' required />
                                <p className={`${login.miniAlert} ${!passwordError ? 'hidden' : ''}`}>{passwordError}</p>
                            </div>
                            <label className={login.verifyCheckbox} htmlFor="agree">
                                <input type="checkbox" id="agree" name='agree' required />
                                <span>
                                    Tôi đồng ý với
                                    <Link href='/terms-of-service'> Điều khoản dịch vụ
                                    </Link> và <Link href="/privacy-policy">
                                        Chính sách bảo mật</Link> .
                                </span>
                            </label>
                            <div>
                                <p className={`${login.miniTitle}`}>
                                    Hoặc đăng nhập bằng
                                </p>
                            </div>
                            <div className={login.socialRow}>
                                <Button className={login.socialBtn} variant="outline-danger" onClick={redirectToGoogleLogin}>
                                    <i className="ti-google" style={{ marginRight: 8 }}></i>
                                    Google
                                </Button>
                                <Button className={login.socialBtn} variant="outline-primary" onClick={redirectToFacebookLogin}>
                                    <i className="ti-facebook" style={{ marginRight: 8 }}></i>
                                    Facebook
                                </Button>
                            </div>
                            <div>
                                <Button className={`${login.registerBtn}`} type='submit'>
                                    Đăng ký
                                </Button>
                            </div>
                            <div>
                                <p className={`${login.miniTitle}`}>
                                    Bạn đã có tài khoản? <Link href='/login' >Đăng nhập</Link>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <NotifyModal show={modalState.show} message={modalState.message} onClose={handleCloseModal} />
        </div>
    )
}
