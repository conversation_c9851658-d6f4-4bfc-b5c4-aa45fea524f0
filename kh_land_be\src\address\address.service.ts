import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Address } from "./Address";
import { Repository } from "typeorm";
import { ERROR_MESSAGES } from "src/utils/error.messages";

@Injectable()
export class AddressService {
    constructor(
        @InjectRepository(Address)
        private readonly addressRepository: Repository<Address>,
    ) { }

    // Dùng khi đã có ID
    async getAddressById(addressId: string): Promise<Address> {
        const address = await this.addressRepository.findOne({
            where: { addressId: addressId },
            relations: [
                'ward',
                'ward.district',
                'ward.district.city',
                'ward.district.city.country',
            ],
        });
        if (!address) {
            throw new NotFoundException([
                {
                    field: 'address',
                    message: 'Err-019',
                    detail: ERROR_MESSAGES['Err-019']
                }
            ]);
        }
        return address;
    }
}
