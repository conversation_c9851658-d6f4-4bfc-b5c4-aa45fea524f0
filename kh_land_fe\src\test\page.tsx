import { useEffect } from "react";
import axios from "axios";

export default function ViewIncrease() {
    const homeId: string = "1e25459e-2899-48db-b88c-3a2f119b135e";  // <PERSON><PERSON> báo homeId

    useEffect(() => {
        // Kiểm tra đã tăng view chưa
        const hasViewed = localStorage.getItem("viewedKey");
        if (hasViewed) return;

        // Sau 5 phút thì mới gửi API tăng view
        const timeoutId = setTimeout(() => {
            axios.post(`/localhost:3081/home/<USER>/increase-view`)  // Gửi yêu cầu API
                .then(() => {
                    localStorage.setItem("viewedKey", "true");  // Lưu vào localStorage
                    console.log("Đã tăng view sau 5 phút");
                })
                .catch((err) => {
                    console.error("Lỗi tăng view:", err);
                });
        }, 5 * 60 * 1000); // 5 phút

        // Dọn timeout nếu người dùng rời trang trước khi đủ 5 phút
        return () => clearTimeout(timeoutId);
    }, [homeId]);  // Thêm homeId vào mảng dependency để theo dõi thay đổi

    return (
        <div>
            <h1>Bài viết #{homeId}</h1>
            {/* Nội dung bài viết */}
        </div>
    );
}
