import { Repository } from 'typeorm';
import { Users } from './Users';
import { LoginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';
export declare class UsersService {
    private readonly userRepository;
    private readonly jwtService;
    constructor(userRepository: Repository<Users>, jwtService: JwtService);
    createUser(userData: Partial<Users>): Promise<Users>;
    getAllUsers(): Promise<Users[]>;
    getUserByEmail(email: string): Promise<Users | null>;
    getUserByPhoneNumber(phoneNumber: string): Promise<Users | null>;
    getUserByEmailOrPhone(username: string): Promise<Users | null>;
    getUserById(userId: string): Promise<Users | null>;
    updateUserById(userId: string, updateUserInfo: Partial<Users>): Promise<Users | null>;
    deleteUserById(userId: string): Promise<Users | null>;
    findOrCreateSocialUser(socialUser: any): Promise<Users>;
    setTwoFactorAuthenticationSecret(secret: string, userId: string): Promise<void>;
    turnOnTwoFactorAuthentication(userId: string): Promise<void>;
    login(dto: LoginDto): Promise<void>;
    verifyOtp(userId: string, otp: string): Promise<void>;
}
