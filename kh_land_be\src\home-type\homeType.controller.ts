import { HomeType } from './HomeType';
import { HomeTypeService } from './homeType.service';
import { Controller, Get, Param } from "@nestjs/common";

@Controller('home-type')
export class HomeTypeController {
    constructor(private readonly homeTypeService: HomeTypeService) { }
    @Get()
    async getAll(): Promise<HomeType[]> {
        return this.homeTypeService.getAllHomeType();
    }
    @Get('home-type/:homeTypeId')
    async findOne(@Param('homeTypeId') homeTypeId: string) {
        return this.homeTypeService.getHomeTypeById(homeTypeId);
    }
}