import { omit } from 'lodash';
import { BadRequestException, Injectable, } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import * as bcrypt from 'bcryptjs';
import { JwtService } from '@nestjs/jwt';
import { JWT_TOKEN_TYPE } from 'src/constant/constants';
import { z } from 'zod';
import { EmailService } from 'src/utils/email.service';
import { ERROR_MESSAGES } from 'src/utils/error.messages';

const ResetTokenPayloadSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  purpose: z.literal(JWT_TOKEN_TYPE.RESET_PASSWORD),
});

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly emailService: EmailService,
  ) { }

  async signup({ firstName, lastName, email, phoneNumber, password }: { firstName: string; lastName: string; email: string; phoneNumber: string; password: string }) {
    const existingUser = await this.usersService.getUserByEmail(email);
    if (existingUser) {
      throw new BadRequestException([
        {
          field: 'email',
          message: 'Err-009',
          detail: ERROR_MESSAGES['Err-009']
        },
      ]);
    }

    const existingPhoneNumber = await this.usersService.getUserByPhoneNumber(phoneNumber);
    if (existingPhoneNumber) {
      throw new BadRequestException([
        {
          field: 'phoneNumber',
          message: 'Err-010',
          detail: ERROR_MESSAGES['Err-010']
        },
      ]);
    }

    const encryptedPassword = await bcrypt.hash(password, 10);
    const user = await this.usersService.createUser({
      firstName,
      lastName,
      email,
      phoneNumber,
      encryptedPassword,
    });

    const token = this.jwtService.sign({ id: user.userId }, { expiresIn: '14d' });
    return { token, user: omit(user, ['encryptedPassword']) };
  }

  async login({ username, password }: { username: string; password: string }) {
    const user = await this.usersService.getUserByEmailOrPhone(username);
    if (!user) {
      throw new BadRequestException([
        {
          field: 'email',
          message: 'Err-011',
          detail: ERROR_MESSAGES['Err-011']
        },
      ]);
    }
    if (user.accountStatus === 'SUSPENDED') {
      throw new BadRequestException([
        {
          field: 'accountStatus',
          message: 'Err-019',
          detail: ERROR_MESSAGES['Err-019']
        },
      ]);
    }

    const match = await bcrypt.compare(password, user.encryptedPassword);
    if (!match) {
      // Increment the number of wrong password attempts
      await this.usersService.updateUserById(user.userId, {
        numberWrongPassword: (user.numberWrongPassword) + 1,
      });
      if (user.numberWrongPassword >= 4) {
        await this.usersService.updateUserById(user.userId, { accountStatus: 'SUSPENDED'as const });
      }
      throw new BadRequestException([
        {
          field: 'password',
          message: 'Err-012',
          detail: ERROR_MESSAGES['Err-012']
        },
      ]);
    } else {
      await this.usersService.updateUserById(user.userId, { numberWrongPassword: 0 }
      );
    }

    const token = this.jwtService.sign({ id: user.userId }, { expiresIn: '14d' });
    return { token, user: omit(user, ['encryptedPassword']) };
  }

  async requestPasswordReset(username: string) {
    const user = await this.usersService.getUserByEmail(username);
    if (!user) {
      throw new BadRequestException([
        {
          field: 'username',
          message: 'Err-017',
          detail: ERROR_MESSAGES['Err-017']
        },
      ]);
    }

    if (user.token) {
      throw new BadRequestException([
        {
          field: 'token',
          message: 'Err-013',
          detail: ERROR_MESSAGES['Err-013']
        },
      ]);
    }

    const resetToken = this.jwtService.sign({
      id: user.userId,
      email: user.email,
      purpose: JWT_TOKEN_TYPE.RESET_PASSWORD,
    }, { expiresIn: '5m' });

    await this.usersService.updateUserById(user.userId, { token: resetToken });

    const template = this.emailService.getPasswordResetTemplate(
      `${user.firstName} ${user.lastName}`,
      user.email,
      resetToken,
    );

    await this.emailService.sendEmail(template);
  }

  async resetPassword(token: string, newPassword: string) {
    const decoded = this.jwtService.verify(token);
    const parsed = ResetTokenPayloadSchema.safeParse(decoded);

    if (!parsed.success) {
      throw new BadRequestException([
        {
          field: 'token',
          message: 'Err-014',
          detail: ERROR_MESSAGES['Err-014']
        },
      ]);
    }

    const { id } = parsed.data;
    const user = await this.usersService.getUserById(id);

    if (!user) {
      throw new BadRequestException([
        {
          field: 'userId',
          message: 'Err-012',
          detail: ERROR_MESSAGES['Err-012']
        },
      ]);
    }

    if (!user.token || user.token !== token) {
      throw new BadRequestException([
        {
          field: 'token',
          message: 'Err-015',
          detail: ERROR_MESSAGES['Err-015']
        },
      ]);
    }

    const same = await bcrypt.compare(newPassword, user.encryptedPassword);
    if (same) {
      throw new BadRequestException([
        {
          field: 'newPassword',
          message: 'Err-016',
          detail: ERROR_MESSAGES['Err-016']
        },
      ]);
    }

    const encrypted = await bcrypt.hash(newPassword, 10);
    await this.usersService.updateUserById(user.userId, {
      encryptedPassword: encrypted,
      token: null,
    });

    return {
      message: 'Password reset successful',
    };
  }
}