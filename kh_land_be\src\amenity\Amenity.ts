import { Column, Entity, Index, JoinTable, ManyToMany } from "typeorm";
import { Home } from "../home/<USER>";


@Entity("amenity", { schema: "public" })
export class Amenity {
  @Column("uuid", {
    primary: true,
    name: "amenity_id",
    default: () => "uuid_generate_v4()",
  })
  amenityId!: string;

  @Column("character varying", { name: "amenity_name", length: 255 })
  amenityName!: string;

  @ManyToMany(() => Home, (home) => home.amenities)
  @JoinTable({
    name: "home_amenity",
    joinColumns: [{ name: "amenity_id", referencedColumnName: "amenityId" }],
    inverseJoinColumns: [{ name: "home_id", referencedColumnName: "homeId" }],
    schema: "public",
  })
  homes!: Home[];
}