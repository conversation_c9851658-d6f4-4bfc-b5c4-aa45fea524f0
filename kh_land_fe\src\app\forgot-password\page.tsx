'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import 'bootstrap/dist/css/bootstrap.min.css'
import { Button } from 'react-bootstrap';
import login from '@/styles/login.module.css'
import { forgotPassword } from '@/app/api/auth/service/authApi';
import NotifyModal from '@/components/NotifyModal';
import { ERROR_MESSAGES } from '@/utils/error.messages';

export default function LoginPage() {
    const router = useRouter();

    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            router.push('/home');
        }
    }, []);

    const [modalState, setModalState] = useState<{
        show: boolean;
        message: string;
        onClose?: () => void;
    }>({
        show: false,
        message: '',
    })

    const openModal = (message: string, onClose?: () => void) => {
        setModalState({ show: true, message, onClose });
    }

    const handleCloseModal = () => {
        setModalState((prev) => {
            const onClose = prev.onClose;
            setTimeout(() => {
                if (onClose) onClose();
            }, 0);
            return { show: false, message: '' };
        });
    }

    const handleBtn = () => {
        router.push("/login");
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        const formData = new FormData(e.currentTarget);
        const username = formData.get('username') as string;
        try {
            await forgotPassword({ username });
            openModal('Đường dẫn thay đổi mật khẩu đã được gửi đến email hoặc số điện thoại của bạn.');
            return;

        } catch (err: any) {
            let errorMsg = 'Đăng nhập thất bại';
            const backendMsg = err?.response?.data?.message;
            if (Array.isArray(backendMsg)) {
                const { message: err_code } = backendMsg[0] || {};
                if (err_code) {
                    errorMsg = ERROR_MESSAGES[err_code] || err_code;
                }
            }
            openModal('Lỗi: ' + errorMsg);
            return;

        }
    }
    return (
        <div className={`${login.background} ${login.fullHeight}`}>
            <div className={login.container}>
                <div className={`row g-0 ${login.fullHeight} ${login.row}`}>
                    <div className={`col-md-6 d-none d-xl-block ${login.fullHeight} ${login.containerLeft}`}>
                        <img src="/img/login-landmark.jpg" alt="Photo" />
                    </div>
                    <div className={`col-12 col-xl-6 ${login.fullHeight} ${login.containerRight}`}>
                        <div className={login.avatarLogo}>
                            <p>
                                KH
                            </p>
                        </div>
                        <p className={login.title}>
                            Quên mật khẩu?
                        </p>
                        <p className={login.subtitle}>
                            điền email gắn với tài khoản của bạn<br />
                            để nhận đường dẫn thay đổi mật khẩu
                        </p>
                        <form className={`${login.inputForm}`} onSubmit={handleSubmit}>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="username">
                                    Tên đăng nhập
                                </label>
                                <input type="text" id="username" name='username' placeholder='Nhập email hoặc số điện thoại của bạn' required />
                            </div>
                            <div>
                                <Button className={`${login.registerBtn} mt-2`} variant="primary" type="submit">
                                    Xác nhận
                                </Button>
                            </div>
                            <div>
                                <Button className={`mt-2 w-100`} variant="outline-primary" onClick={() => handleBtn()}>
                                    quay lại trang đăng nhập
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div >
            <NotifyModal show={modalState.show} message={modalState.message} onClose={handleCloseModal} />
        </div >
    )
}
