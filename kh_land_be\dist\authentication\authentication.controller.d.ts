import { AuthenticationService } from './authentication.service';
import { Request } from 'express';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { Users } from '../users/Users';
export declare class AuthenticationController {
    private authenticationService;
    private jwtService;
    private usersService;
    constructor(authenticationService: AuthenticationService, jwtService: JwtService, usersService: UsersService);
    googleCallback(req: Request): Promise<any>;
    facebookCallback(req: Request): Promise<any>;
    facebookAuth(): Promise<void>;
    verify2FA(body: {
        userId: string;
        otp: string;
    }): Promise<{
        access_token: string;
        user: Users;
    }>;
    login(req: Request): Promise<{
        email: string | undefined;
        access_token: string;
    }>;
    getProfile(req: Request): Express.User | undefined;
    turnOnTwoFactorAuthentication(request: Request, body: {
        twoFactorAuthenticationCode: string;
    }): Promise<{
        message: string;
    }>;
    authenticate(request: Request, body: {
        twoFactorAuthenticationCode: string;
    }): Promise<{
        email: string | undefined;
        access_token: string;
    }>;
    getProtectedResource(req: Request): Express.User | undefined;
    generateTwoFactorAuthentication(request: Request): Promise<{
        success: boolean;
        qr: string;
        tempToken: string;
    }>;
    debugTwoFactorAuthenticationUrl(request: Request): Promise<{
        success: boolean;
        otpauthUrl: string;
        secret: string;
    }>;
    getRawTwoFactorAuthenticationUrl(request: Request): Promise<{
        success: boolean;
        otpauthUrl: string;
        secret: string;
    }>;
}
