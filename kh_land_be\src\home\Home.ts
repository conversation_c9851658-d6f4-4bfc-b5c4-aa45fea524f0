import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Address } from "../address/Address";
import { HomeType } from "../home-type/HomeType";
import { Amenity } from "../amenity/Amenity";
import { HomePhoto } from "../home-photo/HomePhoto";
import { Seller } from "../seller/Seller";
import { Users } from "../users/Users";

@Index("idx_home_address_id", ["addressId"], {})
@Index("idx_home_home_type_id", ["homeTypeId"], {})
@Entity("home", { schema: "public" })
export class Home {
  @Column("uuid", {
    primary: true,
    name: "home_id",
    default: () => "uuid_generate_v4()",
  })
  homeId!: string;

  @Column("uuid", { name: "home_type_id", nullable: true })
  homeTypeId!: string | null;

  @Column("bigint", { name: "price", nullable: true })
  price!: string | null;

  @Column("uuid", { name: "address_id", nullable: true })
  addressId!: string | null;

  @Column("integer", { name: "square_feet", nullable: true })
  squareFeet!: number | null;

  @Column("character varying", {
    name: "contact_information",
    nullable: true,
    length: 15,
  })
  contactInformation!: string | null;

  @Column("character varying", {
    name: "home_name",
    nullable: true,
    length: 255,
  })
  homeName!: string | null;

  @Column("integer", { name: "views", nullable: true, default: () => "0" })
  views!: number | null;

  @Column("integer", { name: "priority", nullable: true, default: () => "0" })
  priority!: number | null;

  @Column("timestamp without time zone", {
    name: "created_at",
    nullable: true,
    default: () => "now()",
  })
  createdAt!: Date | null;

  @Column("timestamp without time zone", {
    name: "updated_at",
    nullable: true,
    default: () => "now()",
  })
  updatedAt!: Date | null;

  @Column("timestamp without time zone", { name: "deleted_at", nullable: true })
  deletedAt!: Date | null;

  @Column("enum", {
    name: "home_status",
    nullable: true,
    enum: ["FOR_SALE", "FOR_RENT", "UNAVAILABLE"],
  })
  homeStatus!: "FOR_SALE" | "FOR_RENT" | "UNAVAILABLE" | null;

  @Column("text", { name: "home_description", nullable: true })
  homeDescription!: string | null;

  @ManyToOne(() => Address, (address) => address.homes, {
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  })
  @JoinColumn([{ name: "address_id", referencedColumnName: "addressId" }])
  address!: Address;

  @ManyToOne(() => HomeType, (homeType) => homeType.homes)
  @JoinColumn([{ name: "home_type_id", referencedColumnName: "homeTypeId" }])
  homeType!: HomeType;

  @ManyToOne(() => Seller, (seller) => seller.homes)
  @JoinColumn([{ name: "seller_id", referencedColumnName: "sellerId" }])
  seller!: Seller;

  @ManyToMany(() => Amenity, (amenity) => amenity.homes)
  amenities!: Amenity[];

  @OneToMany(() => HomePhoto, (homePhoto) => homePhoto.home)
  homePhotos!: HomePhoto[];

  @ManyToMany(() => Users, (users) => users.homes)
  @JoinTable({
    name: "wish_list",
    joinColumns: [{ name: "home_id", referencedColumnName: "homeId" }],
    inverseJoinColumns: [{ name: "user_id", referencedColumnName: "userId" }],
    schema: "public",
  })
  users!: Users[];
}
