import React from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

export default function Section_2() {
 
  return (
    <section className="py-5 bg-white" style={{ height: 596 }}>
  <div className="container">
    <h2 className="text-center fw-bold mb-5"><PERSON><PERSON><PERSON> thức hoạt động</h2>
    <div className="row align-items-center">
      <div className="col-md-6">
        {[
          {
            step: 1,
            title: 'Đăng ký tài khoản',
            desc: 'Tạo tài khoản miễn phí chỉ trong vài bước đơn giản.',
          },
          {
            step: 2,
            title: 'Tìm kiếm bất động sản',
            desc: '<PERSON><PERSON> dụng bộ lọc mạnh mẽ để tìm kiếm theo nhu cầu của bạn.',
          },
          {
            step: 3,
            title: '<PERSON><PERSON> thông tin chi tiết',
            desc: '<PERSON><PERSON><PERSON> cập thông tin đầy đủ, hình ảnh và video về bất động sản.',
          },
          {
            step: 4,
            title: 'Liên hệ người bán/cho thuê',
            desc: 'Kết nối trực tiếp với chủ sở hữu hoặc môi giới thông qua nền tảng.',
          },
        ].map((item, idx) => (
          <div className="d-flex gap-3 mb-4" key={idx}>
            <div
              className="rounded-circle text-white d-flex align-items-center justify-content-center"
              style={{ width: '36px', height: '36px', fontWeight: 'bold' , backgroundColor: '#636AE8FF' } }
            >
              {item.step}
            </div>
            <div>
              <h6 className="mb-1 fw-semibold">{item.title}</h6>
              <p className="text-muted mb-0">{item.desc}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="col-md-6 text-center">
        <img
          src="/img/building-1.jpg"
          alt="building"
          className="img-fluid rounded shadow-sm"
        />
      </div>
    </div>
  </div>
</section>
  );
}