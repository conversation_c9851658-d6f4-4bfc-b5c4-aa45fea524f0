import { Profile, Strategy } from 'passport-google-oauth20';
import { AuthenticationService } from '../authentication.service';
declare const GoogleStrategy_base: new (...args: [options: import("passport-google-oauth20").StrategyOptionsWithRequest] | [options: import("passport-google-oauth20").StrategyOptions] | [options: import("passport-google-oauth20").StrategyOptions] | [options: import("passport-google-oauth20").StrategyOptionsWithRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class GoogleStrategy extends GoogleStrategy_base {
    private authenticationService;
    constructor(authenticationService: AuthenticationService);
    validate(accessToken: string, refreshToken: string, profile: Profile): Promise<any>;
}
export {};
