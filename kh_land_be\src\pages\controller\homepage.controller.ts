import { Controller, Get } from '@nestjs/common';
import { HomeService } from 'src/home/<USER>';
import { NewsService } from 'src/news/news.service';

@Controller()
export class HomepageController {
    constructor(
        private readonly homeService: HomeService,
        private readonly newsService: NewsService,
    ) { }

    @Get()
    async getHomepageData() {
        const numberOfHomes = 1;
        const numberOfNews = 1;
        const homes = await this.getHomeList(numberOfHomes);
        const news = await this.getNewsList(numberOfNews);
        return { homes, news };
    }

    async getHomeList(numberOfHomes: number = 3) {
        const [
            mostViewed,
            lastAdded,
            recommended,
            wishList
        ] = await Promise.all([
            this.homeService.getMostViewedHomes(numberOfHomes),
            this.homeService.getLastAddedHomes(numberOfHomes),
            this.homeService.getRecommendedHomes(numberOfHomes),
            this.homeService.getHostPropertiesHome(numberOfHomes),
        ]);
        return { mostViewed, lastAdded, recommended, wishList };
    }

    async getNewsList(numberOfNews: number = 3) {
        const [
            latestNews,
            mostViewedNews,
            recommendedNews
        ] = await Promise.all([
            this.newsService.getLatestNews(numberOfNews),
            this.newsService.getMostViewedNews(numberOfNews),
            this.newsService.getRecommendedNews(numberOfNews),
        ]);
        return { latestNews, mostViewedNews, recommendedNews };
    }
}
