"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeController = void 0;
const common_1 = require("@nestjs/common");
const home_service_1 = require("./home.service");
const home_dto_1 = require("./dto/home.dto");
let HomeController = class HomeController {
    constructor(homeService) {
        this.homeService = homeService;
    }
    async getAll() {
        return this.homeService.getAllHomes();
    }
    async getHomeByFilter(filter) {
        const numberOfHomes = 1;
        if (filter === 'properties') {
            return this.homeService.getHostPropertiesHome(numberOfHomes);
        }
        return this.homeService.getListHomeOrderBy(filter, numberOfHomes);
    }
    async findOne(homeId) {
        return this.homeService.getHomeById(homeId);
    }
    async create(body) {
        return this.homeService.createHome(body);
    }
    async update(homeId, body) {
        return this.homeService.updateHomeById(homeId, body);
    }
    async remove(homeId) {
        return this.homeService.deleteHomeById(homeId);
    }
};
exports.HomeController = HomeController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "getAll", null);
__decorate([
    (0, common_1.Get)('filter/:filter'),
    __param(0, (0, common_1.Param)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "getHomeByFilter", null);
__decorate([
    (0, common_1.Get)('home-id/:homeId'),
    __param(0, (0, common_1.Param)('homeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [home_dto_1.HomeDto]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('home-update/:homeId'),
    __param(0, (0, common_1.Param)('homeId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('home-delete/:homeId'),
    __param(0, (0, common_1.Param)('homeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HomeController.prototype, "remove", null);
exports.HomeController = HomeController = __decorate([
    (0, common_1.Controller)('home'),
    __metadata("design:paramtypes", [home_service_1.HomeService])
], HomeController);
//# sourceMappingURL=home.controller.js.map