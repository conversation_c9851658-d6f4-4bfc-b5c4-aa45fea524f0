"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterPageController = void 0;
const common_1 = require("@nestjs/common");
const home_service_1 = require("../../home/<USER>");
const filter_dto_1 = require("../dto/filter.dto");
const error_messages_1 = require("../../utils/error.messages");
let FilterPageController = class FilterPageController {
    constructor(homeService) {
        this.homeService = homeService;
    }
    async searchHome(searchParams) {
        const pageSize = 10;
        const home = await this.homeService.searchByFilter(searchParams, pageSize);
        if (!home) {
            throw new common_1.NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-017',
                    detail: error_messages_1.ERROR_MESSAGES['Err-017']
                }
            ]);
        }
        return home;
    }
};
exports.FilterPageController = FilterPageController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_dto_1.FilterPageDto]),
    __metadata("design:returntype", Promise)
], FilterPageController.prototype, "searchHome", null);
exports.FilterPageController = FilterPageController = __decorate([
    (0, common_1.Controller)('find'),
    __metadata("design:paramtypes", [home_service_1.HomeService])
], FilterPageController);
//# sourceMappingURL=filterpage.controller.js.map