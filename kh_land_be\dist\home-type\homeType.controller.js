"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeTypeController = void 0;
const homeType_service_1 = require("./homeType.service");
const common_1 = require("@nestjs/common");
let HomeTypeController = class HomeTypeController {
    constructor(homeTypeService) {
        this.homeTypeService = homeTypeService;
    }
    async getAll() {
        return this.homeTypeService.getAllHomeType();
    }
    async findOne(homeTypeId) {
        return this.homeTypeService.getHomeTypeById(homeTypeId);
    }
};
exports.HomeTypeController = HomeTypeController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HomeTypeController.prototype, "getAll", null);
__decorate([
    (0, common_1.Get)('home-type/:homeTypeId'),
    __param(0, (0, common_1.Param)('homeTypeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HomeTypeController.prototype, "findOne", null);
exports.HomeTypeController = HomeTypeController = __decorate([
    (0, common_1.Controller)('home-type'),
    __metadata("design:paramtypes", [homeType_service_1.HomeTypeService])
], HomeTypeController);
//# sourceMappingURL=homeType.controller.js.map