{"version": 3, "file": "google.strategy.js", "sourceRoot": "", "sources": ["../../../src/authentication/strategies/google.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAAoD;AACpD,qEAA4D;AAC5D,2CAAmE;AACnE,sEAAkE;AAG3D,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,IAAA,2BAAgB,EAAC,kCAAQ,EAAE,QAAQ,CAAC;IACtE,YAAoB,qBAA4C;QAC9D,KAAK,CAAC;YACJ,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAiB;YACvC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAqB;YAC/C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAoB;YAC7C,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;SAC5B,CAAC,CAAC;QANe,0BAAqB,GAArB,qBAAqB,CAAuB;IAOhE,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,WAAmB,EACnB,YAAoB,EACpB,OAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;YACzC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;YACjC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,8BAAqB,CAAC,kCAAkC,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;oBACtD,KAAK;oBACL,SAAS,EAAE,IAAI,EAAE,SAAS,IAAI,SAAS;oBACvC,QAAQ,EAAE,IAAI,EAAE,UAAU,IAAI,MAAM;oBACpC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK;oBAC7B,QAAQ,EAAE,QAAQ;oBAClB,UAAU,EAAE,OAAO,CAAC,EAAE;iBACvB,CAAC,CAAC;YACL,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,8BAAqB,CAAC,oCAAoC,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;CACF,CAAA;AAvCY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAEgC,8CAAqB;GADrD,cAAc,CAuC1B"}