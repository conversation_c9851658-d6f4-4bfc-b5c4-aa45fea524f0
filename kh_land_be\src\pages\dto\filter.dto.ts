import { Type } from "class-transformer";
import { IsNumber, IsOptional, IsString, Matches, Min } from "class-validator";

export class FilterPageDto {
    @IsString()
    @IsOptional()
    homeTypeId?: string;

    @IsString()
    @IsOptional()
    amenityId?: string;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    squareFeetMin?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    squareFeetMax?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    bathroomMin?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    bathroomMax?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    bedroomMin?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    bedroomMax?: number;

    @IsString()
    @IsOptional()
    @Matches(/^\d+$/)
    priceMin?: string;

    @IsString()
    @IsOptional()
    @Matches(/^\d+$/)
    priceMax?: string;

    @IsString()
    @IsOptional()
    searchKeyword?: string;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(1)
    pageNumber?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    wardId?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    districtId?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    cityId?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    countryId?: number;
}