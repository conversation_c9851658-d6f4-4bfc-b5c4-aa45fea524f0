"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Home = void 0;
const typeorm_1 = require("typeorm");
const Address_1 = require("../address/Address");
const HomeType_1 = require("../home-type/HomeType");
const Amenity_1 = require("../amenity/Amenity");
const HomePhoto_1 = require("../home-photo/HomePhoto");
const Seller_1 = require("../seller/Seller");
const Users_1 = require("../users/Users");
let Home = class Home {
};
exports.Home = Home;
__decorate([
    (0, typeorm_1.Column)("uuid", {
        primary: true,
        name: "home_id",
        default: () => "uuid_generate_v4()",
    }),
    __metadata("design:type", String)
], Home.prototype, "homeId", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "home_type_id", nullable: true }),
    __metadata("design:type", Object)
], Home.prototype, "homeTypeId", void 0);
__decorate([
    (0, typeorm_1.Column)("bigint", { name: "price", nullable: true }),
    __metadata("design:type", Object)
], Home.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "address_id", nullable: true }),
    __metadata("design:type", Object)
], Home.prototype, "addressId", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "square_feet", nullable: true }),
    __metadata("design:type", Object)
], Home.prototype, "squareFeet", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", {
        name: "contact_information",
        nullable: true,
        length: 15,
    }),
    __metadata("design:type", Object)
], Home.prototype, "contactInformation", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", {
        name: "home_name",
        nullable: true,
        length: 255,
    }),
    __metadata("design:type", Object)
], Home.prototype, "homeName", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "views", nullable: true, default: () => "0" }),
    __metadata("design:type", Object)
], Home.prototype, "views", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "priority", nullable: true, default: () => "0" }),
    __metadata("design:type", Object)
], Home.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", {
        name: "created_at",
        nullable: true,
        default: () => "now()",
    }),
    __metadata("design:type", Object)
], Home.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", {
        name: "updated_at",
        nullable: true,
        default: () => "now()",
    }),
    __metadata("design:type", Object)
], Home.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", { name: "deleted_at", nullable: true }),
    __metadata("design:type", Object)
], Home.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.Column)("enum", {
        name: "home_status",
        nullable: true,
        enum: ["FOR_SALE", "FOR_RENT", "UNAVAILABLE"],
    }),
    __metadata("design:type", Object)
], Home.prototype, "homeStatus", void 0);
__decorate([
    (0, typeorm_1.Column)("text", { name: "home_description", nullable: true }),
    __metadata("design:type", Object)
], Home.prototype, "homeDescription", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Address_1.Address, (address) => address.homes, {
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)([{ name: "address_id", referencedColumnName: "addressId" }]),
    __metadata("design:type", Address_1.Address)
], Home.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => HomeType_1.HomeType, (homeType) => homeType.homes),
    (0, typeorm_1.JoinColumn)([{ name: "home_type_id", referencedColumnName: "homeTypeId" }]),
    __metadata("design:type", HomeType_1.HomeType)
], Home.prototype, "homeType", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Seller_1.Seller, (seller) => seller.homes),
    (0, typeorm_1.JoinColumn)([{ name: "seller_id", referencedColumnName: "sellerId" }]),
    __metadata("design:type", Seller_1.Seller)
], Home.prototype, "seller", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => Amenity_1.Amenity, (amenity) => amenity.homes),
    __metadata("design:type", Array)
], Home.prototype, "amenities", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => HomePhoto_1.HomePhoto, (homePhoto) => homePhoto.home),
    __metadata("design:type", Array)
], Home.prototype, "homePhotos", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => Users_1.Users, (users) => users.homes),
    (0, typeorm_1.JoinTable)({
        name: "wish_list",
        joinColumns: [{ name: "home_id", referencedColumnName: "homeId" }],
        inverseJoinColumns: [{ name: "user_id", referencedColumnName: "userId" }],
        schema: "public",
    }),
    __metadata("design:type", Array)
], Home.prototype, "users", void 0);
exports.Home = Home = __decorate([
    (0, typeorm_1.Index)("idx_home_address_id", ["addressId"], {}),
    (0, typeorm_1.Index)("idx_home_home_type_id", ["homeTypeId"], {}),
    (0, typeorm_1.Entity)("home", { schema: "public" })
], Home);
//# sourceMappingURL=Home.js.map