import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import { Users } from './Users';
import { LoginDto } from './dto/login.dto';
import * as bcrypt from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import nodemailer from 'nodemailer';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,
    private readonly jwtService: JwtService,
  ) { }

  async createUser(userData: Partial<Users>): Promise<Users> {
    const newUser = this.userRepository.create(userData);
    return this.userRepository.save(newUser);
  }

  async getAllUsers(): Promise<Users[]> {
    return this.userRepository.find({
      where: { deletedAt: IsNull() },
    });
  }

  async getUserByEmail(email: string): Promise<Users | null> {
    return this.userRepository.findOne({
      where: { email, deletedAt: IsNull() },
    });
  }

  async getUserByPhoneNumber(phoneNumber: string): Promise<Users | null> {
    return this.userRepository.findOne({
      where: { phoneNumber, deletedAt: IsNull() },
    });
  }

  async getUserByEmailOrPhone(username: string): Promise<Users | null> {
    return this.userRepository.findOne({
      where: [
        { email: username, deletedAt: IsNull() },
        { phoneNumber: username, deletedAt: IsNull() },
      ],
    });
  }

  async getUserById(userId: string): Promise<Users | null> {
    return this.userRepository.findOne({
      where: { userId, deletedAt: IsNull() },
    });
  }

  async updateUserById(userId: string, updateUserInfo: Partial<Users>): Promise<Users | null> {
    await this.userRepository.update({ userId, deletedAt: IsNull() }, updateUserInfo);
    return this.userRepository.findOne({ where: { userId, deletedAt: IsNull() } });
  }

  async deleteUserById(userId: string): Promise<Users | null> {
    await this.userRepository.softDelete({ userId });
    return this.userRepository.findOne({ where: { userId }, withDeleted: true });
  }
//thanhmtce
  async findOrCreateSocialUser(socialUser: any): Promise<Users> {
    let user = await this.userRepository.findOne({
      where: { provider: socialUser.provider, providerId: socialUser.providerId },
    });

    if (!user) {
      user = this.userRepository.create({
        provider: socialUser.provider,
        providerId: socialUser.providerId,
        email: socialUser.email,
        firstName: socialUser.firstName,
        lastName: socialUser.lastName,
        avatarUrl: socialUser.avatarUrl,
        // Các trường mặc định khác nếu cần
      });
      await this.userRepository.save(user);
    }

    return user;
  }

  async setTwoFactorAuthenticationSecret(secret: string, userId: string) {
    await this.userRepository.update(
      { userId },
      { twoFactorAuthenticationSecret: secret }
    );
  }

  async turnOnTwoFactorAuthentication(userId: string) {
    await this.userRepository.update(
      { userId },
      { isTwoFactorAuthenticationEnabled: true }
    );
  }

  async login(dto: LoginDto) {
    // Xử lý đăng nhập ở đây hoặc chuyển sang AuthenticationService nếu cần
  }

  async verifyOtp(userId: string, otp: string) {
    // Xử lý xác thực OTP ở đây hoặc chuyển sang AuthenticationService nếu cần
  }
}
