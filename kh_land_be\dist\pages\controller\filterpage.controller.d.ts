import { HomeService } from 'src/home/<USER>';
import { FilterPageDto } from '../dto/filter.dto';
export declare class FilterPageController {
    private readonly homeService;
    constructor(homeService: HomeService);
    searchHome(searchParams: FilterPageDto): Promise<{
        homeId: string;
        homeName: string | null;
        price: string | null;
        squareFeet: number | null;
        homeType: {
            homeTypeName: string | null;
        };
        amenities: {
            amenityName: string;
        }[];
        homeDescription: string | null;
        districtName: string | null;
        cityName: string | null;
    }[]>;
}
