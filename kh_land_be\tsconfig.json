{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2020", // <PERSON>ên dùng ES2020 để tương thích tốt hơn với NestJS
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strict": true, // Bật strict mode tăng độ an toàn
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": true, // bậtbật để tránh lỗi không khai báo kiểu dữ liệu
    "strictBindCallApply": true,
    "noFallthroughCasesInSwitch": true,
    "esModuleInterop": true, // Thường cần cho import các thư viện ngoài
    "moduleResolution": "node", // Đảm bảo module resolution phù hợp
    "types": ["node", "jest"]
  },
  "exclude": ["node_modules", "dist"]
}
