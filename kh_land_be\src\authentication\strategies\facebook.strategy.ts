import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { Strategy, Profile, VerifyFunction } from 'passport-facebook';
import { AuthenticationService } from '../authentication.service';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  constructor(private authenticationService: AuthenticationService) {
    const clientID = process.env.FACEBOOK_CLIENT_ID;
    const clientSecret = process.env.FACEBOOK_CLIENT_SECRET;
    const callbackURL = process.env.FACEBOOK_CALLBACK_URL || 'http://localhost:3001/auth/facebook/callback';

    if (!clientID || !clientSecret || !callbackURL) {
      throw new Error('Facebook OAuth environment variables are not set properly.');
    }

    super({
      clientID,
      clientSecret,
      callbackURL,
      profileFields: ['id', 'emails', 'name', 'displayName', 'photos'],
      scope: ['email'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: Profile): Promise<any> {
    const { emails, displayName, photos, id } = profile;
    const user = {
      email: emails && emails[0]?.value,
      name: displayName,
      avatar: photos && photos[0]?.value,
      provider: 'facebook',
      providerId: id,
    };
    return user;
  }
}
