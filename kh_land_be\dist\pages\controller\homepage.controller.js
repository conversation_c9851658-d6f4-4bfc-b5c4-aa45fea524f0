"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomepageController = void 0;
const common_1 = require("@nestjs/common");
const home_service_1 = require("../../home/<USER>");
const news_service_1 = require("../../news/news.service");
let HomepageController = class HomepageController {
    constructor(homeService, newsService) {
        this.homeService = homeService;
        this.newsService = newsService;
    }
    async getHomepageData() {
        const numberOfHomes = 1;
        const numberOfNews = 1;
        const homes = await this.getHomeList(numberOfHomes);
        const news = await this.getNewsList(numberOfNews);
        return { homes, news };
    }
    async getHomeList(numberOfHomes = 3) {
        const [mostViewed, lastAdded, recommended, wishList] = await Promise.all([
            this.homeService.getMostViewedHomes(numberOfHomes),
            this.homeService.getLastAddedHomes(numberOfHomes),
            this.homeService.getRecommendedHomes(numberOfHomes),
            this.homeService.getHostPropertiesHome(numberOfHomes),
        ]);
        return { mostViewed, lastAdded, recommended, wishList };
    }
    async getNewsList(numberOfNews = 3) {
        const [latestNews, mostViewedNews, recommendedNews] = await Promise.all([
            this.newsService.getLatestNews(numberOfNews),
            this.newsService.getMostViewedNews(numberOfNews),
            this.newsService.getRecommendedNews(numberOfNews),
        ]);
        return { latestNews, mostViewedNews, recommendedNews };
    }
};
exports.HomepageController = HomepageController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HomepageController.prototype, "getHomepageData", null);
exports.HomepageController = HomepageController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [home_service_1.HomeService,
        news_service_1.NewsService])
], HomepageController);
//# sourceMappingURL=homepage.controller.js.map