import React from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import { useRouter } from 'next/navigation';


export default function OverlayRegister() {
    const router = useRouter()
  return (
    <div
      style={{
        width: '764px',
    height: '514px',
    backgroundColor: '#f4f5f7',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '2rem',
      }}
    >
      {/* Grid card 2x2 */}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',

          gap: '2rem',
          maxWidth: '800px',
          filter: 'blur(2px)',
          opacity: 0.4,
        }}
      >
        {/* Card 1 */}
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            border: '1px solid #EBEBEA',
            boxShadow:
              '0px 0px 1px rgba(23, 26, 31, 0.07), 0px 0px 2px rgba(23, 26, 31, 0.12)',
            padding: '1.25rem',
            minWidth: '320px',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '0.75rem',
            }}
          >
            <strong>Hoạt động gần đây</strong>
            <a
              href="#"
              style={{
                fontSize: '0.875rem',
                color: '#636AE8',
                textDecoration: 'none',
              }}
            >
              Xem tất cả
            </a>
          </div>
          <ul style={{ paddingLeft: '1rem', fontSize: '0.9rem' }}>
            <li>Đã xem "Căn hộ cao cấp view Landmark 81"</li>
            <li>Đã lưu "Biệt thự nghỉ dưỡng ven hồ"</li>
            <li>Đã xem "Nhà riêng ngõ yên tĩnh, gần phố cổ"</li>
            <li>Đã gửi yêu cầu tư vấn</li>
          </ul>
        </div>

        {/* Card 2 */}
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            border: '1px solid #EBEBEA',
            boxShadow:
              '0px 0px 1px rgba(23, 26, 31, 0.07), 0px 0px 2px rgba(23, 26, 31, 0.12)',
            padding: '1.25rem',
            minWidth: '320px',
          }}
        >
          <div
            style={{
              marginBottom: '0.75rem',
              fontWeight: 'bold',
            }}
          >
            Tổng quan thị trường
          </div>
          <p>Giá trung bình Q2/2024: <b>7 tỷ</b></p>
          <div
            style={{
              width: '100%',
              height: '100px',
              backgroundColor: '#eaeaea',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#888',
              fontSize: '0.875rem',
            }}
          >
            (Biểu đồ minh hoạ)
          </div>
        </div>

        {/* Card 3 */}
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            border: '1px solid #EBEBEA',
            boxShadow:
              '0px 0px 1px rgba(23, 26, 31, 0.07), 0px 0px 2px rgba(23, 26, 31, 0.12)',
            padding: '1.25rem',
            minWidth: '320px',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '0.75rem',
            }}
          >
            <strong>Mục đã lưu</strong>
            <a
              href="#"
              style={{
                fontSize: '0.875rem',
                color: '#636AE8',
                textDecoration: 'none',
              }}
            >
              Xem tất cả
            </a>
          </div>
          <ul style={{ paddingLeft: '1rem', fontSize: '0.9rem' }}>
            <li>Căn hộ 2PN, Quận 2, TP.HCM</li>
            <li>Shophouse mặt biển Hạ Long</li>
            <li>Nhà riêng Hà Nội, 3PN</li>
          </ul>
        </div>

        {/* Card 4 */}
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            border: '1px solid #EBEBEA',
            boxShadow:
              '0px 0px 1px rgba(23, 26, 31, 0.07), 0px 0px 2px rgba(23, 26, 31, 0.12)',
            padding: '1.25rem',
            minWidth: '320px',
          }}
        >
          <strong>Hoàn thiện hồ sơ</strong>
          <p style={{ fontSize: '0.9rem' }}>Tiến độ: 75%</p>
          <div className="progress" style={{ height: '6px' }}>
            <div
              className="progress-bar"
              style={{ width: '75%', backgroundColor: '#636AE8' }}
            />
          </div>
          <button className="btn btn-outline-primary btn-sm mt-3">
            Cập nhật hồ sơ
          </button>
        </div>
      </div>

      {/* Nút đăng ký */}
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10,
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 0 20px rgba(0,0,0,0.2)',
          textAlign: 'center',
        }}
      >
        <h4 className="mb-3">Đăng ký để tiếp tục</h4>
        <button
          className="btn btn-primary"
           onClick={() => router.push('/signup')}
        >
          Đăng ký ngay
        </button>
      </div>
    </div>
  );
}