import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  Delete,
  Put,
} from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { Users } from './Users';
import { AuthenticationService } from '../authentication/authentication.service';

@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly authenticationService: AuthenticationService,
  ) { }

  @Post()
  async create(@Body() body: Partial<Users>): Promise<Users> {
    return this.usersService.createUser(body);
  }

  @Get()
  async findAll(): Promise<Users[]> {
    return this.usersService.getAllUsers();
  }

  @Get(':userId')
  async findOne(@Param('userId') userId: string): Promise<Users | null> {
    return this.usersService.getUserById(userId);
  }

  @Put(':userId')
  async update(
    @Param('userId') userId: string,
    @Body() body: Partial<Users>,
  ): Promise<Users | null> {
    return this.usersService.updateUserById(userId, body);
  }

  @Delete(':userId')
  async remove(@Param('userId') userId: string): Promise<Users | null> {
    return this.usersService.deleteUserById(userId);
  }

  @Post('login')
  async login(@Body() dto: LoginDto) {
    return this.authenticationService.login(dto);
  }

  @Post('verify-otp')
  async verifyOtp(@Body() body: { userId: string; otp: string }) {
    return this.authenticationService.verifyOtp(body.userId, body.otp);
  }
}
