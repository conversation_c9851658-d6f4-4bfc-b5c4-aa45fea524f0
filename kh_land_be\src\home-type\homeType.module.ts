import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { HomeType } from "./HomeType";
import { HomeTypeController } from "./homeType.controller";
import { HomeTypeService } from "./homeType.service";

@Module({
    imports: [TypeOrmModule.forFeature([HomeType])],
    controllers: [HomeTypeController],
    providers: [HomeTypeService],
    exports: [HomeTypeService],
})
export class HomeTypeModule { }