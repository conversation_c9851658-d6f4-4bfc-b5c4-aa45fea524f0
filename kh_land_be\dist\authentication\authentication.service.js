"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Jwt2faStrategy = exports.AuthenticationService = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("../users/users.service");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = __importStar(require("bcrypt"));
const otplib_1 = require("otplib");
const qrcode_1 = require("qrcode");
const passport_jwt_1 = require("passport-jwt");
const passport_1 = require("@nestjs/passport");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let AuthenticationService = class AuthenticationService {
    constructor(usersService, jwtService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
    }
    async validateUser(email, pass) {
        const user = await this.usersService.getUserByEmail(email);
        if (!user || !user.password)
            return null;
        const isMatch = await bcrypt.compare(pass, user.password);
        if (isMatch) {
            const { password, ...userWithoutPassword } = user;
            return userWithoutPassword;
        }
        return null;
    }
    async login(userWithoutPsw) {
        const payload = {
            sub: userWithoutPsw.userId,
            email: userWithoutPsw.email,
        };
        return {
            email: payload.email,
            access_token: this.jwtService.sign(payload),
        };
    }
    async loginWith2fa(userWithoutPsw) {
        const payload = {
            email: userWithoutPsw.email,
            isTwoFactorAuthenticationEnabled: !!userWithoutPsw.isTwoFactorAuthenticationEnabled,
            isTwoFactorAuthenticated: true,
        };
        return {
            email: payload.email,
            access_token: this.jwtService.sign(payload),
        };
    }
    async generateTwoFactorAuthenticationSecret(user) {
        const secret = otplib_1.authenticator.generateSecret();
        const appName = process.env.AUTH_APP_NAME || 'KH Land App';
        const otpauthUrl = otplib_1.authenticator.keyuri(user.email, appName, secret);
        console.log('otpauthUrl for testing:', otpauthUrl);
        await this.usersService.setTwoFactorAuthenticationSecret(secret, user.userId);
        return {
            secret,
            otpauthUrl,
        };
    }
    async generateQrCodeDataURL(otpAuthUrl) {
        return (0, qrcode_1.toDataURL)(otpAuthUrl);
    }
    async validateSocialLogin(socialUser) {
        let user = await this.usersService.findOrCreateSocialUser(socialUser);
        if (user.twoFactorAuthenticationSecret) {
            return { require2FA: true, userId: user.userId };
        }
        const payload = { sub: user.userId, email: user.email };
        return {
            access_token: this.jwtService.sign(payload),
            user,
        };
    }
    isTwoFactorAuthenticationCodeValid(twoFactorAuthenticationCode, user) {
        if (!user.twoFactorAuthenticationSecret)
            return false;
        return otplib_1.authenticator.verify({
            token: twoFactorAuthenticationCode,
            secret: user.twoFactorAuthenticationSecret,
        });
    }
    async verifyOtp(userId, otp) {
        const user = await this.usersService.getUserById(userId);
        if (!user || !user.twoFactorAuthenticationSecret) {
            throw new common_2.UnauthorizedException('User or 2FA secret not found');
        }
        const isValid = otplib_1.authenticator.verify({
            token: otp,
            secret: user.twoFactorAuthenticationSecret,
        });
        if (!isValid) {
            throw new common_2.UnauthorizedException('Invalid OTP');
        }
        return true;
    }
    async findByEmail(email) {
        return this.usersService.getUserByEmail(email);
    }
    async createOAuthUser(data) {
        return this.usersService.createUser(data);
    }
};
exports.AuthenticationService = AuthenticationService;
exports.AuthenticationService = AuthenticationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService])
], AuthenticationService);
let Jwt2faStrategy = class Jwt2faStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy, 'jwt-2fa') {
    constructor(userService, configService) {
        const jwtSecret = configService.get('JWT_SECRET');
        if (!jwtSecret) {
            throw new Error('JWT_SECRET is not defined');
        }
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            secretOrKey: jwtSecret,
        });
        this.userService = userService;
    }
    async validate(payload) {
        const user = await this.userService.getUserById(payload.sub);
        if (!user) {
            throw new common_2.UnauthorizedException('User not found');
        }
        if (!user.isTwoFactorAuthenticationEnabled) {
            return user;
        }
        if (payload.isTwoFactorAuthenticated) {
            return user;
        }
        throw new common_2.UnauthorizedException('2FA authentication required');
    }
};
exports.Jwt2faStrategy = Jwt2faStrategy;
exports.Jwt2faStrategy = Jwt2faStrategy = __decorate([
    (0, common_2.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        config_1.ConfigService])
], Jwt2faStrategy);
//# sourceMappingURL=authentication.service.js.map