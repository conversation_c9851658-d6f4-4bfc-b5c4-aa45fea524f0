import { InjectRepository } from '@nestjs/typeorm';
import { HomeType } from './HomeType';
import { Injectable, NotFoundException } from "@nestjs/common";
import { Repository } from 'typeorm';

@Injectable()
export class HomeTypeService {
    constructor(
        @InjectRepository(HomeType)
        private readonly homeTypeRepository: Repository<HomeType>,

    ) { }

    async getAllHomeType(): Promise<HomeType[]> {
        const homes = await this.homeTypeRepository.find({
        });
        return homes;
    }

    async getHomeTypeById(homeTypeId: string): Promise<HomeType> {
        try {
            const homeType = await this.homeTypeRepository.findOne({
                where: { homeTypeId }
            })
            if (!homeType) {
                throw new NotFoundException([
                    {
                        field: 'homeTypeId',
                        message: 'Err-018',
                    },
                ]);
            }
            return homeType;
        } catch (error) {
            throw new NotFoundException([
                {
                    field: 'homeTypeId',
                    message: 'Err-023',
                },
            ]);
        }
    }
}