import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { News } from './News';
import { AddressService } from '../address/address.service';

@Injectable()
export class NewsService {
    constructor(
        @InjectRepository(News)
        private readonly newsRepository: Repository<News>,
        private readonly addressService: AddressService,
    ) { }
    async createNews(newsData: Partial<News>): Promise<News> {
        const newNews = this.newsRepository.create(newsData);
        return this.newsRepository.save(newNews);
    }
    async getAllNews(): Promise<News[]> {
        const homes = await this.newsRepository.find({
            where: { newsStatus: 'ACTIVATE' },
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const home of homes) {
            home.address = await this.addressService.getAddressById(home.address.addressId);
        }

        return homes;
    }
    async getNewsById(newsId: string): Promise<News | null> {
        const news = await this.newsRepository.findOne({
            where: { newsId },
            relations: ['author', 'address', 'newsPhotos'],
        });
        if (!news) return null;
        news.address = await this.addressService.getAddressById(news.address.addressId);
        return news;
    }
    async updateNewsById(newsId: string, updateNewsInfo: Partial<News>): Promise<News | null> {
        await this.newsRepository.update({ newsId }, updateNewsInfo);
        return this.newsRepository.findOne({
            where: { newsId },
            relations: ['author', 'address', 'newsPhotos'],
        });
    }
    async deleteNewsById(newsId: string): Promise<News | null> {
        const news = await this.getNewsById(newsId);
        if (!news) return null;
        await this.newsRepository.delete({ newsId });
        return news;
    }
    async getLatestNews(limit: number = 10): Promise<News[]> {
        const news = await this.newsRepository.find({
            where: { newsStatus: 'ACTIVATE' },
            order: { createdAt: 'DESC' },
            take: limit,
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const n of news) {
            n.address = await this.addressService.getAddressById(n.address.addressId);
        }

        return news;
    }
    async getMostViewedNews(limit: number = 10): Promise<News[]> {
        const news = await this.newsRepository.find({
            order: { views: 'DESC' },
            where: { newsStatus: 'ACTIVATE' },
            take: limit,
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const n of news) {
            n.address = await this.addressService.getAddressById(n.address.addressId);
        }

        return news;
    }
    async getRecommendedNews(limit: number = 10): Promise<News[]> {
        const news = await this.newsRepository.find({
            where: { newsStatus: 'ACTIVATE' },
            order: { priority: 'DESC' },
            take: limit,
            relations: ['author', 'address', 'newsPhotos'],
        });
        for (const n of news) {
            n.address = await this.addressService.getAddressById(n.address.addressId);
        }

        return news;
    }
}