import { Column, Entity, Index, OneToMany } from "typeorm";
import { Home } from "../home/<USER>";


@Entity("home_type", { schema: "public" })
export class HomeType {
  @Column("uuid", {
    primary: true,
    name: "home_type_id",
    default: () => "uuid_generate_v4()",
  })
  homeTypeId!: string;

  @Column("character varying", { name: "home_type_name", nullable: true, length: 255 })
  homeTypeName!: string | null;

  @OneToMany(() => Home, (home) => home.homeType)
  homes!: Home[];
}
