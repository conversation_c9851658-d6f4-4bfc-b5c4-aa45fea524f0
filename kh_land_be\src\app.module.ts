import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from './users/users.module';
import { HomeModule } from './home/<USER>';
import { NewsModule } from './news/news.module';
import { HomepageModule } from './pages/module/homepage.module';
import { HomeTypeModule } from './home-type/homeType.module';
import { FilterPageModule } from './pages/module/filterpage.module';
import { DataSource } from 'typeorm';
import { Users } from './users/Users';
import { WishList } from './wishlist/WishList';
import { Blogger } from './blogger/Blogger';
import { Seller } from './seller/Seller';
import { News } from './news/News';
import { Home } from './home/<USER>';
import { Address } from './address/Address';
import { HomeType } from './home-type/HomeType';
import { Amenity } from './amenity/Amenity';
import { HomePhoto } from './home-photo/HomePhoto';
import { Wards } from './address/wards/Wards';
import { Districts } from './address/wards/districts/Districts';
import { Cities } from './address/wards/districts/cities/Cities';
import { Countries } from './address/wards/districts/cities/countries/Countries';
import { NewsPhoto } from './news-photo/NewsPhoto';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      entities: [Users, WishList, Blogger, Seller, News, Home, Address, HomeType, Amenity, HomePhoto, Wards, Districts, Cities, Countries, NewsPhoto], // Thêm NewsPhoto entity
      synchronize: true, // chỉ dùng cho dev - đã loại bỏ các index xung đột
    }),
    UsersModule,
    HomeModule,
    NewsModule,
    HomepageModule,
    HomeTypeModule,
    FilterPageModule,
    TypeOrmModule.forFeature([Users, WishList, Blogger, Seller, News, Home, Address, HomeType, Amenity, HomePhoto, Wards, Districts, Cities, Countries, NewsPhoto]), // Thêm NewsPhoto entity
  ],
  controllers: [],
})
export class AppModule {
  constructor(private dataSource: DataSource) {
    console.log('Connected to DB:', this.dataSource.options.database);
  }
}
