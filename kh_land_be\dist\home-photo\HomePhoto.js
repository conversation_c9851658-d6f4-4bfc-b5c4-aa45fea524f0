"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomePhoto = void 0;
const typeorm_1 = require("typeorm");
const Home_1 = require("../home/<USER>");
let HomePhoto = class HomePhoto {
};
exports.HomePhoto = HomePhoto;
__decorate([
    (0, typeorm_1.Column)("uuid", {
        primary: true,
        name: "photo_id",
        default: () => "uuid_generate_v4()",
    }),
    __metadata("design:type", String)
], HomePhoto.prototype, "photoId", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "home_id" }),
    __metadata("design:type", String)
], HomePhoto.prototype, "homeId", void 0);
__decorate([
    (0, typeorm_1.Column)("text", { name: "photo_url" }),
    __metadata("design:type", String)
], HomePhoto.prototype, "photoUrl", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", {
        name: "uploaded_at",
        nullable: true,
        default: () => "now()",
    }),
    __metadata("design:type", Object)
], HomePhoto.prototype, "uploadedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Home_1.Home, (home) => home.homePhotos, { onDelete: "CASCADE" }),
    (0, typeorm_1.JoinColumn)([{ name: "home_id", referencedColumnName: "homeId" }]),
    __metadata("design:type", Home_1.Home)
], HomePhoto.prototype, "home", void 0);
exports.HomePhoto = HomePhoto = __decorate([
    (0, typeorm_1.Index)("idx_home_photo_home_id", ["homeId"], {}),
    (0, typeorm_1.Entity)("home_photo", { schema: "public" })
], HomePhoto);
//# sourceMappingURL=HomePhoto.js.map