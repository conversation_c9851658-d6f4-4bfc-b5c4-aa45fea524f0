import { Column, <PERSON><PERSON>ty, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Home } from "../home/<USER>";

@Index("idx_home_photo_home_id", ["homeId"], {})
@Entity("home_photo", { schema: "public" })
export class HomePhoto {
  @Column("uuid", {
    primary: true,
    name: "photo_id",
    default: () => "uuid_generate_v4()",
  })
  photoId!: string;

  @Column("uuid", { name: "home_id" })
  homeId!: string;

  @Column("text", { name: "photo_url" })
  photoUrl!: string;

  @Column("timestamp without time zone", {
    name: "uploaded_at",
    nullable: true,
    default: () => "now()",
  })
  uploadedAt!: Date | null;

  @ManyToOne(() => Home, (home) => home.homePhotos, { onDelete: "CASCADE" })
  @JoinColumn([{ name: "home_id", referencedColumnName: "homeId" }])
  home!: Home;
}
