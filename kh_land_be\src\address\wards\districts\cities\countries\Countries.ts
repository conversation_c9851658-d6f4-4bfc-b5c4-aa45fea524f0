import {
  Column,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Cities } from "../Cities";

@Index("unique_country_name", ["countryName"], { unique: true })
@Entity("countries", { schema: "public" })
export class Countries {
  @PrimaryGeneratedColumn({ type: "integer", name: "country_id" })
  countryId!: number;

  @Column("character varying", { name: "country_name", unique: true })
  countryName!: string;

  @OneToMany(() => Cities, (cities) => cities.country)
  cities!: Cities[];
}
