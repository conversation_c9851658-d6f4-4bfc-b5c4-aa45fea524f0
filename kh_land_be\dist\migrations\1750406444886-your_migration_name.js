"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.YourMigrationName1750406444886 = void 0;
class YourMigrationName1750406444886 {
    constructor() {
        this.name = 'YourMigrationName1750406444886';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "countries" ("country_id" SERIAL NOT NULL, "name" character varying NOT NULL, CONSTRAINT "UQ_fa1376321185575cf2226b1491d" UNIQUE ("name"), CONSTRAINT "PK_9886b09af4b4724d595b2e3923c" PRIMARY KEY ("country_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "countries_name_key" ON "countries" ("name") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "unique_country_name" ON "countries" ("name") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "countries_pkey" ON "countries" ("country_id") `);
        await queryRunner.query(`CREATE TABLE "cities" ("city_id" SERIAL NOT NULL, "name" character varying NOT NULL, "country_id" integer NOT NULL, CONSTRAINT "PK_2cea114a4d9e4edb1a69d4eb079" PRIMARY KEY ("city_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_cities_country_id" ON "cities" ("country_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "unique_city_name_country" ON "cities" ("country_id", "name") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "cities_pkey" ON "cities" ("city_id") `);
        await queryRunner.query(`CREATE TABLE "districts" ("district_id" SERIAL NOT NULL, "name" character varying NOT NULL, "city_id" integer NOT NULL, CONSTRAINT "PK_99d9f2ae5540b699f2f56446549" PRIMARY KEY ("district_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "districts_pkey" ON "districts" ("district_id") `);
        await queryRunner.query(`CREATE INDEX "idx_districts_city_id" ON "districts" ("city_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "unique_district_name_city" ON "districts" ("city_id", "name") `);
        await queryRunner.query(`CREATE TABLE "wards" ("ward_id" SERIAL NOT NULL, "name" character varying NOT NULL, "district_id" integer NOT NULL, CONSTRAINT "PK_28dcc1ae74acdd9221dbb4de9f7" PRIMARY KEY ("ward_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "wards_pkey" ON "wards" ("ward_id") `);
        await queryRunner.query(`CREATE INDEX "idx_wards_district_id" ON "wards" ("district_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "unique_ward_name_district" ON "wards" ("district_id", "name") `);
        await queryRunner.query(`CREATE TABLE "home_type" ("home_type_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "home_type_name" character varying(255), CONSTRAINT "PK_8234e708666b8161e6a3bbf4363" PRIMARY KEY ("home_type_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "home_type_pkey" ON "home_type" ("home_type_id") `);
        await queryRunner.query(`CREATE TABLE "amenity" ("amenity_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(255) NOT NULL, CONSTRAINT "PK_a6ff18badc3dad084aa323775c5" PRIMARY KEY ("amenity_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "amenity_pkey" ON "amenity" ("amenity_id") `);
        await queryRunner.query(`CREATE TABLE "home_photo" ("photo_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "photo_url" text NOT NULL, "uploaded_at" TIMESTAMP DEFAULT now(), "home_id" uuid NOT NULL, CONSTRAINT "PK_281b756243cc34c0f453ad9cbf5" PRIMARY KEY ("photo_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "home_photo_pkey" ON "home_photo" ("photo_id") `);
        await queryRunner.query(`CREATE INDEX "idx_home_photo_home_id" ON "home_photo" ("home_id") `);
        await queryRunner.query(`CREATE TABLE "news_photo" ("photo_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "photo_url" text NOT NULL, "uploaded_at" TIMESTAMP DEFAULT now(), "news_id" uuid NOT NULL, CONSTRAINT "PK_3394c4fdf78e6286b69c0c66251" PRIMARY KEY ("photo_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "news_photo_pkey" ON "news_photo" ("photo_id") `);
        await queryRunner.query(`CREATE INDEX "idx_news_photo_news_id" ON "news_photo" ("news_id") `);
        await queryRunner.query(`CREATE TABLE "blogger" ("blogger_id" uuid NOT NULL, "blog_id" uuid NOT NULL, CONSTRAINT "PK_c88f545f67c90ba00c2e911ab18" PRIMARY KEY ("blogger_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "blogger_pkey" ON "blogger" ("blogger_id") `);
        await queryRunner.query(`CREATE INDEX "idx_blogger_blog_id" ON "blogger" ("blog_id") `);
        await queryRunner.query(`CREATE TYPE "public"."news_news_status_enum" AS ENUM('ACTIVATE', 'BANNED', 'UNAVAILABLE')`);
        await queryRunner.query(`CREATE TABLE "news" ("news_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying(255) NOT NULL, "content" text NOT NULL, "published_at" date DEFAULT ('now'::text)::date, "source" character varying(255), "priority" integer DEFAULT 1, "created_at" TIMESTAMP DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "author_id" uuid NOT NULL, "address_id" uuid NOT NULL, "deleted_at" TIMESTAMP, "views" integer DEFAULT 0, "news_status" "public"."news_news_status_enum" NOT NULL, CONSTRAINT "PK_313a1b4b0d8af7de07bfb46b6cb" PRIMARY KEY ("news_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "news_pkey" ON "news" ("news_id") `);
        await queryRunner.query(`CREATE INDEX "idx_news_author_id" ON "news" ("author_id") `);
        await queryRunner.query(`CREATE INDEX "idx_news_address_id" ON "news" ("address_id") `);
        await queryRunner.query(`CREATE TABLE "wish_list" ("home_id" uuid NOT NULL, "user_id" uuid NOT NULL, CONSTRAINT "PK_105eae83a30d88b900421d35cbc" PRIMARY KEY ("home_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_wish_list_user_id" ON "wish_list" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "idx_wish_list_home_id" ON "wish_list" ("home_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "wish_list_pkey" ON "wish_list" ("home_id", "user_id") `);
        await queryRunner.query(`CREATE TYPE "public"."users_account_status_enum" AS ENUM('ACTIVE', 'SUSPENDED')`);
        await queryRunner.query(`CREATE TABLE "users" ("user_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "avatar_url" text, "role" "public"."users_role_enum" NOT NULL DEFAULT '() => "'USER'"', "created_at" TIMESTAMP DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "deleted_at" TIMESTAMP, "provider" character varying(50), "provider_id" character varying(255), "otp" character varying(10), "otpCreatedAt" TIMESTAMP, "totpSecret" character varying(64), "first_name" character varying(255) NOT NULL, "last_name" character varying(255) NOT NULL, "email" character varying(255) NOT NULL, "encrypted_password" character varying(255) NOT NULL, "phone_number" character varying(15) NOT NULL, "token" text, "number_wrong_password" integer DEFAULT 0, "account_status" "public"."users_account_status_enum" NOT NULL DEFAULT '() => "'ACTIVE'"', CONSTRAINT "PK_96aac72f1574b88752e9fb00089" PRIMARY KEY ("user_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "users_pkey" ON "users" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "seller" ("seller_id" uuid NOT NULL, "selled_building" integer, "home_id" uuid NOT NULL, CONSTRAINT "PK_69e6f9bd54b1eb98c71001ab040" PRIMARY KEY ("seller_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "seller_pkey" ON "seller" ("seller_id") `);
        await queryRunner.query(`CREATE INDEX "idx_seller_home_id" ON "seller" ("home_id") `);
        await queryRunner.query(`CREATE TYPE "public"."home_home_status_enum" AS ENUM('FOR_RENT', 'FOR_SALE', 'UNAVAILABLE')`);
        await queryRunner.query(`CREATE TABLE "home" ("home_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "home_name" character varying(255), "home_description" character varying(255), "price" bigint, "square_feet" integer, "priority" integer DEFAULT 0, "views" integer DEFAULT 0, "contact_information" character varying(15), "created_at" TIMESTAMP DEFAULT now(), "bedroom" integer, "bathroom" integer, "deleted_at" TIMESTAMP, "home_status" "public"."home_home_status_enum" NOT NULL, "address_id" uuid NOT NULL, "home_type_id" uuid, CONSTRAINT "PK_f20a7acb7d39715a24fb8cb9ccc" PRIMARY KEY ("home_id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "home_pkey" ON "home" ("home_id") `);
        await queryRunner.query(`CREATE INDEX "idx_home_address_id" ON "home" ("address_id") `);
        await queryRunner.query(`CREATE TABLE "address" ("address_id" uuid NOT NULL DEFAULT gen_random_uuid(), "latitude" numeric, "longitude" numeric, "street" character varying, "ward_id" integer, CONSTRAINT "PK_db4aae0a059fd4ef7709cb802b0" PRIMARY KEY ("address_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_address_ward_id" ON "address" ("ward_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "address_pkey" ON "address" ("address_id") `);
        await queryRunner.query(`CREATE TABLE "home_amenity" ("amenity_id" uuid NOT NULL, "home_id" uuid NOT NULL, CONSTRAINT "PK_e59ef027c04f89ecd730267c9ab" PRIMARY KEY ("amenity_id", "home_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d0d99274e0b845955b612c5086" ON "home_amenity" ("amenity_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_42fb10d2dd931654c8e0fec278" ON "home_amenity" ("home_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_ebc705df79e0c31e15dad918fd" ON "wish_list" ("home_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c23debb14a44001e4c5ffb3169" ON "wish_list" ("user_id") `);
        await queryRunner.query(`ALTER TABLE "cities" ADD CONSTRAINT "FK_4aa0d9a52c36ff93415934e2d2b" FOREIGN KEY ("country_id") REFERENCES "countries"("country_id") ON DELETE RESTRICT ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "districts" ADD CONSTRAINT "FK_d7d1704cfb8bc19fb0d9c2f7ced" FOREIGN KEY ("city_id") REFERENCES "cities"("city_id") ON DELETE RESTRICT ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "wards" ADD CONSTRAINT "FK_3d1ef92876a28d10ac2d3fe766b" FOREIGN KEY ("district_id") REFERENCES "districts"("district_id") ON DELETE RESTRICT ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "home_photo" ADD CONSTRAINT "FK_3e1a4d471849d58ea7141f77de8" FOREIGN KEY ("home_id") REFERENCES "home"("home_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "news_photo" ADD CONSTRAINT "FK_db842da9beacc885a3d0b35421e" FOREIGN KEY ("news_id") REFERENCES "news"("news_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blogger" ADD CONSTRAINT "FK_7b6b4ba71f25d14f4649348deca" FOREIGN KEY ("blog_id") REFERENCES "news"("news_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blogger" ADD CONSTRAINT "FK_c88f545f67c90ba00c2e911ab18" FOREIGN KEY ("blogger_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "news" ADD CONSTRAINT "FK_716defa6946d00499223732ba83" FOREIGN KEY ("address_id") REFERENCES "address"("address_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "news" ADD CONSTRAINT "FK_173d93468ebf142bb3424c2fd63" FOREIGN KEY ("author_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "wish_list" ADD CONSTRAINT "FK_c23debb14a44001e4c5ffb3169d" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "seller" ADD CONSTRAINT "FK_f9b0e413bc723d209674b2c49a3" FOREIGN KEY ("home_id") REFERENCES "home"("home_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "seller" ADD CONSTRAINT "FK_69e6f9bd54b1eb98c71001ab040" FOREIGN KEY ("seller_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "home" ADD CONSTRAINT "FK_67dcdb16b45f45401f032a732ae" FOREIGN KEY ("address_id") REFERENCES "address"("address_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "home" ADD CONSTRAINT "FK_3a0132852ce810afa7529f675f2" FOREIGN KEY ("home_type_id") REFERENCES "home_type"("home_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "address" ADD CONSTRAINT "FK_e5ad8623648a0deb50ddf4e9550" FOREIGN KEY ("ward_id") REFERENCES "wards"("ward_id") ON DELETE SET NULL ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "home_amenity" ADD CONSTRAINT "FK_d0d99274e0b845955b612c50860" FOREIGN KEY ("amenity_id") REFERENCES "amenity"("amenity_id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "home_amenity" ADD CONSTRAINT "FK_42fb10d2dd931654c8e0fec2785" FOREIGN KEY ("home_id") REFERENCES "home"("home_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "wish_list" ADD CONSTRAINT "FK_ebc705df79e0c31e15dad918fde" FOREIGN KEY ("home_id") REFERENCES "home"("home_id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "wish_list" DROP CONSTRAINT "FK_ebc705df79e0c31e15dad918fde"`);
        await queryRunner.query(`ALTER TABLE "home_amenity" DROP CONSTRAINT "FK_42fb10d2dd931654c8e0fec2785"`);
        await queryRunner.query(`ALTER TABLE "home_amenity" DROP CONSTRAINT "FK_d0d99274e0b845955b612c50860"`);
        await queryRunner.query(`ALTER TABLE "address" DROP CONSTRAINT "FK_e5ad8623648a0deb50ddf4e9550"`);
        await queryRunner.query(`ALTER TABLE "home" DROP CONSTRAINT "FK_3a0132852ce810afa7529f675f2"`);
        await queryRunner.query(`ALTER TABLE "home" DROP CONSTRAINT "FK_67dcdb16b45f45401f032a732ae"`);
        await queryRunner.query(`ALTER TABLE "seller" DROP CONSTRAINT "FK_69e6f9bd54b1eb98c71001ab040"`);
        await queryRunner.query(`ALTER TABLE "seller" DROP CONSTRAINT "FK_f9b0e413bc723d209674b2c49a3"`);
        await queryRunner.query(`ALTER TABLE "wish_list" DROP CONSTRAINT "FK_c23debb14a44001e4c5ffb3169d"`);
        await queryRunner.query(`ALTER TABLE "news" DROP CONSTRAINT "FK_173d93468ebf142bb3424c2fd63"`);
        await queryRunner.query(`ALTER TABLE "news" DROP CONSTRAINT "FK_716defa6946d00499223732ba83"`);
        await queryRunner.query(`ALTER TABLE "blogger" DROP CONSTRAINT "FK_c88f545f67c90ba00c2e911ab18"`);
        await queryRunner.query(`ALTER TABLE "blogger" DROP CONSTRAINT "FK_7b6b4ba71f25d14f4649348deca"`);
        await queryRunner.query(`ALTER TABLE "news_photo" DROP CONSTRAINT "FK_db842da9beacc885a3d0b35421e"`);
        await queryRunner.query(`ALTER TABLE "home_photo" DROP CONSTRAINT "FK_3e1a4d471849d58ea7141f77de8"`);
        await queryRunner.query(`ALTER TABLE "wards" DROP CONSTRAINT "FK_3d1ef92876a28d10ac2d3fe766b"`);
        await queryRunner.query(`ALTER TABLE "districts" DROP CONSTRAINT "FK_d7d1704cfb8bc19fb0d9c2f7ced"`);
        await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "FK_4aa0d9a52c36ff93415934e2d2b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c23debb14a44001e4c5ffb3169"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ebc705df79e0c31e15dad918fd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_42fb10d2dd931654c8e0fec278"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d0d99274e0b845955b612c5086"`);
        await queryRunner.query(`DROP TABLE "home_amenity"`);
        await queryRunner.query(`DROP INDEX "public"."address_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."idx_address_ward_id"`);
        await queryRunner.query(`DROP TABLE "address"`);
        await queryRunner.query(`DROP INDEX "public"."idx_home_address_id"`);
        await queryRunner.query(`DROP INDEX "public"."home_pkey"`);
        await queryRunner.query(`DROP TABLE "home"`);
        await queryRunner.query(`DROP TYPE "public"."home_home_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."idx_seller_home_id"`);
        await queryRunner.query(`DROP INDEX "public"."seller_pkey"`);
        await queryRunner.query(`DROP TABLE "seller"`);
        await queryRunner.query(`DROP INDEX "public"."users_pkey"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "public"."users_account_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."wish_list_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."idx_wish_list_home_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_wish_list_user_id"`);
        await queryRunner.query(`DROP TABLE "wish_list"`);
        await queryRunner.query(`DROP INDEX "public"."idx_news_address_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_news_author_id"`);
        await queryRunner.query(`DROP INDEX "public"."news_pkey"`);
        await queryRunner.query(`DROP TABLE "news"`);
        await queryRunner.query(`DROP TYPE "public"."news_news_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."idx_blogger_blog_id"`);
        await queryRunner.query(`DROP INDEX "public"."blogger_pkey"`);
        await queryRunner.query(`DROP TABLE "blogger"`);
        await queryRunner.query(`DROP INDEX "public"."idx_news_photo_news_id"`);
        await queryRunner.query(`DROP INDEX "public"."news_photo_pkey"`);
        await queryRunner.query(`DROP TABLE "news_photo"`);
        await queryRunner.query(`DROP INDEX "public"."idx_home_photo_home_id"`);
        await queryRunner.query(`DROP INDEX "public"."home_photo_pkey"`);
        await queryRunner.query(`DROP TABLE "home_photo"`);
        await queryRunner.query(`DROP INDEX "public"."amenity_pkey"`);
        await queryRunner.query(`DROP TABLE "amenity"`);
        await queryRunner.query(`DROP INDEX "public"."home_type_pkey"`);
        await queryRunner.query(`DROP TABLE "home_type"`);
        await queryRunner.query(`DROP INDEX "public"."unique_ward_name_district"`);
        await queryRunner.query(`DROP INDEX "public"."idx_wards_district_id"`);
        await queryRunner.query(`DROP INDEX "public"."wards_pkey"`);
        await queryRunner.query(`DROP TABLE "wards"`);
        await queryRunner.query(`DROP INDEX "public"."unique_district_name_city"`);
        await queryRunner.query(`DROP INDEX "public"."idx_districts_city_id"`);
        await queryRunner.query(`DROP INDEX "public"."districts_pkey"`);
        await queryRunner.query(`DROP TABLE "districts"`);
        await queryRunner.query(`DROP INDEX "public"."cities_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."unique_city_name_country"`);
        await queryRunner.query(`DROP INDEX "public"."idx_cities_country_id"`);
        await queryRunner.query(`DROP TABLE "cities"`);
        await queryRunner.query(`DROP INDEX "public"."countries_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."unique_country_name"`);
        await queryRunner.query(`DROP INDEX "public"."countries_name_key"`);
        await queryRunner.query(`DROP TABLE "countries"`);
    }
}
exports.YourMigrationName1750406444886 = YourMigrationName1750406444886;
//# sourceMappingURL=1750406444886-your_migration_name.js.map