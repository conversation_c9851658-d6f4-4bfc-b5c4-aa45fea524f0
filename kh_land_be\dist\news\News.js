"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.News = void 0;
const typeorm_1 = require("typeorm");
const Address_1 = require("../address/Address");
const Users_1 = require("../users/Users");
const NewsPhoto_1 = require("../news-photo/NewsPhoto");
const Blogger_1 = require("../blogger/Blogger");
let News = class News {
};
exports.News = News;
__decorate([
    (0, typeorm_1.Column)("uuid", {
        primary: true,
        name: "news_id",
        default: () => "uuid_generate_v4()",
    }),
    __metadata("design:type", String)
], News.prototype, "newsId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "title", length: 255 }),
    __metadata("design:type", String)
], News.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)("text", { name: "content" }),
    __metadata("design:type", String)
], News.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)("date", {
        name: "published_at",
        nullable: true,
        default: () => "CURRENT_DATE",
    }),
    __metadata("design:type", Object)
], News.prototype, "publishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "author_id", nullable: true }),
    __metadata("design:type", Object)
], News.prototype, "authorId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "source", nullable: true, length: 255 }),
    __metadata("design:type", Object)
], News.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "address_id", nullable: true }),
    __metadata("design:type", Object)
], News.prototype, "addressId", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "priority", nullable: true, default: () => "1" }),
    __metadata("design:type", Object)
], News.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", {
        name: "created_at",
        nullable: true,
        default: () => "now()",
    }),
    __metadata("design:type", Object)
], News.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", {
        name: "updated_at",
        nullable: true,
        default: () => "now()",
    }),
    __metadata("design:type", Object)
], News.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", { name: "deleted_at", nullable: true }),
    __metadata("design:type", Object)
], News.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "views", nullable: true, default: () => "0" }),
    __metadata("design:type", Object)
], News.prototype, "views", void 0);
__decorate([
    (0, typeorm_1.Column)("enum", {
        name: "news_status",
        nullable: true,
        enum: ["ACTIVATE", "BANNED", "UNAVAILABLE"],
        default: "ACTIVATE",
    }),
    __metadata("design:type", Object)
], News.prototype, "newsStatus", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Blogger_1.Blogger, (blogger) => blogger.blog),
    __metadata("design:type", Array)
], News.prototype, "bloggers", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Address_1.Address, (address) => address.news, {
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)([{ name: "address_id", referencedColumnName: "addressId" }]),
    __metadata("design:type", Address_1.Address)
], News.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Users_1.Users, (users) => users.news),
    (0, typeorm_1.JoinColumn)([{ name: "author_id", referencedColumnName: "userId" }]),
    __metadata("design:type", Users_1.Users)
], News.prototype, "author", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => NewsPhoto_1.NewsPhoto, (newsPhoto) => newsPhoto.news),
    __metadata("design:type", Array)
], News.prototype, "newsPhotos", void 0);
exports.News = News = __decorate([
    (0, typeorm_1.Index)("idx_news_address_id", ["addressId"], {}),
    (0, typeorm_1.Index)("idx_news_author_id", ["authorId"], {}),
    (0, typeorm_1.Entity)("news", { schema: "public" })
], News);
//# sourceMappingURL=News.js.map