import { Column, Entity, Index, JoinColumn, ManyToOne } from "typeorm";
import { News } from "../news/News";

@Index("idx_news_photo_news_id", ["newsId"], {})
@Entity("news_photo", { schema: "public" })
export class NewsPhoto {
  @Column("uuid", {
    primary: true,
    name: "photo_id",
    default: () => "uuid_generate_v4()",
  })
  photoId!: string;

  @Column("uuid", { name: "news_id" })
  newsId!: string;

  @Column("text", { name: "photo_url" })
  photoUrl!: string;

  @Column("timestamp without time zone", {
    name: "uploaded_at",
    nullable: true,
    default: () => "now()",
  })
  uploadedAt!: Date | null;

  @ManyToOne(() => News, (news) => news.newsPhotos)
  @JoinColumn([{ name: "news_id", referencedColumnName: "newsId" }])
  news!: News;
}
