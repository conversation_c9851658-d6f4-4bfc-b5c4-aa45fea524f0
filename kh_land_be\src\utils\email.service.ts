//@ts-check
import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      auth: {
        user: process.env.MAIL_USER,
        pass: process.env.MAIL_PASS,
      }
    });
  }

  getPasswordResetTemplate(name: string, email: string, token: string) {
    const link = `${process.env.WEB_URL}/reset-password?token=${token}`;
    return {
      email,
      subject: 'Reset Your Password',
      body: `
        <h1>Reset Your Password</h1>
        <p>Hi ${name},</p>
        <p>We received a request to reset your password for your account. To proceed, please click the link below:</p>
        <a href="${link}" target="_blank">Reset Password</a>
        <p>For security reasons, this link will expire in 5 minutes. Please DO NOT SHARE it with anyone.</p>
      `,
    };
  }

  async sendEmail({
    email,
    subject,
    body,
  }: {
    email: string;
    subject: string;
    body: string;
  }) {
    await this.transporter.sendMail({
      from: process.env.MAIL_USER,
      to: email,
      subject,
      html: body,
    });
  }
}
