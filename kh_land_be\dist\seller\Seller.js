"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Seller = void 0;
const typeorm_1 = require("typeorm");
const Home_1 = require("../home/<USER>");
const Users_1 = require("../users/Users");
let Seller = class Seller {
};
exports.Seller = Seller;
__decorate([
    (0, typeorm_1.Column)("uuid", { primary: true, name: "seller_id" }),
    __metadata("design:type", String)
], Seller.prototype, "sellerId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Home_1.Home, (home) => home.seller),
    __metadata("design:type", Array)
], Seller.prototype, "homes", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => Users_1.Users, (users) => users.seller, { onDelete: "CASCADE" }),
    (0, typeorm_1.JoinColumn)([{ name: "seller_id", referencedColumnName: "userId" }]),
    __metadata("design:type", Users_1.Users)
], Seller.prototype, "seller", void 0);
exports.Seller = Seller = __decorate([
    (0, typeorm_1.Entity)("seller", { schema: "public" })
], Seller);
//# sourceMappingURL=Seller.js.map