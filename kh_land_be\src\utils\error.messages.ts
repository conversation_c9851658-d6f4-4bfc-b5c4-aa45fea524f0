export const ERROR_MESSAGES: Record<string, string> = {
    'Err-000': 'This field is required',
    'Err-001': 'Password must be between 8 and 20 characters',
    'Err-002': 'Password must include at least 1 uppercase letter',
    'Err-003': 'Password must include at least 1 lowercase letter',
    'Err-004': 'Password must include at least 1 digit',
    'Err-005': 'Password must include at least 1 special character (!@#$%^&?)',
    'Err-006': 'Phone number format is invalid',
    'Err-007': 'Email format is invalid',
    'Err-008': 'Username must be a valid email or phone number',
    'Err-009': 'Email is already taken',
    'Err-010': 'Phone number is already taken',
    'Err-011': 'User not found',
    'Err-012': 'Wrong password',
    'Err-013': 'Password reset already requested',
    'Err-014': 'Invalid token payload',
    'Err-015': 'Token has already been used or is invalid',
    'Err-016': 'New password must differ from old password',
    'Err-017': 'Home not found',
    'Err-018': 'Home type not found',
    'Err-019': 'Address not found',
    'Err-020': 'Update failed',
    'Err-021': 'Home id is invalid',
    'Err-022': 'Home not found',
    'Err-023': 'Home type id is invalid',
};
