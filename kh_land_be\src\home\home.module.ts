import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Home } from "./Home";
import { HomeController } from "./home.controller";
import { HomeService } from "./home.service";
import { AddressModule } from "src/address/address.module";
import { NewsModule } from "src/news/news.module";
import { HomeType } from "src/home-type/HomeType";
import { Blogger } from "src/blogger/Blogger";
import { HomePhoto } from "src/home-photo/HomePhoto";
import { Amenity } from "src/amenity/Amenity";
import { Address } from "src/address/Address";
import { NewsPhoto } from "src/news-photo/NewsPhoto";
import { News } from "src/news/News";
import { Seller } from "src/seller/Seller";

@Module({
    imports: [TypeOrmModule.forFeature([Home
        //Để tạm khi nào có module thì xóa
        , <PERSON>ller,
        News,
        NewsPhoto,
        Address,
        Home,
        Amenity,
        HomePhoto,
        Blogger
    ]),
        AddressModule,
        NewsModule
    ],
    controllers: [HomeController],
    providers: [HomeService],
    exports: [HomeService],
})
export class HomeModule { }