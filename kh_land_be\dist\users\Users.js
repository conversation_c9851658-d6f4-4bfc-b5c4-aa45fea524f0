"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Users = void 0;
const typeorm_1 = require("typeorm");
const News_1 = require("../news/News");
const Seller_1 = require("../seller/Seller");
const Home_1 = require("../home/<USER>");
const Blogger_1 = require("../blogger/Blogger");
const WishList_1 = require("../wishlist/WishList");
let Users = class Users {
};
exports.Users = Users;
__decorate([
    (0, typeorm_1.Column)('uuid', {
        primary: true,
        name: 'user_id',
        default: () => 'uuid_generate_v4()',
    }),
    __metadata("design:type", String)
], Users.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'provider', nullable: true }),
    __metadata("design:type", String)
], Users.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'provider_id', nullable: true }),
    __metadata("design:type", String)
], Users.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'email', nullable: true }),
    __metadata("design:type", String)
], Users.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { name: 'avatar_url', nullable: true }),
    __metadata("design:type", Object)
], Users.prototype, "avatarUrl", void 0);
__decorate([
    (0, typeorm_1.Column)('enum', {
        name: 'role',
        enum: ['USER', 'ADMIN', 'SELLER', 'BLOGGER'],
        default: 'USER',
    }),
    __metadata("design:type", String)
], Users.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp without time zone', {
        name: 'created_at',
        nullable: true,
        default: () => 'now()',
    }),
    __metadata("design:type", Object)
], Users.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp without time zone', {
        name: 'updated_at',
        nullable: true,
        default: () => 'now()',
    }),
    __metadata("design:type", Object)
], Users.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp without time zone', { name: 'deleted_at', nullable: true }),
    __metadata("design:type", Object)
], Users.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', { name: 'otp', nullable: true, length: 10 }),
    __metadata("design:type", Object)
], Users.prototype, "otp", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp without time zone', {
        name: 'otpCreatedAt',
        nullable: true,
    }),
    __metadata("design:type", Object)
], Users.prototype, "otpCreatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', {
        name: 'totpSecret',
        nullable: true,
        length: 64,
    }),
    __metadata("design:type", Object)
], Users.prototype, "totpSecret", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', { name: 'first_name', length: 255, nullable: true }),
    __metadata("design:type", Object)
], Users.prototype, "firstName", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', { name: 'last_name', length: 255, nullable: true }),
    __metadata("design:type", Object)
], Users.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', { name: 'encrypted_password', length: 255, nullable: true }),
    __metadata("design:type", Object)
], Users.prototype, "encryptedPassword", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { name: 'token', nullable: true }),
    __metadata("design:type", Object)
], Users.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)('integer', { name: 'number_wrong_password', default: () => '0' }),
    __metadata("design:type", Number)
], Users.prototype, "numberWrongPassword", void 0);
__decorate([
    (0, typeorm_1.Column)('enum', {
        name: 'account_status',
        enum: ['ACTIVE', 'SUSPENDED'],
        default: 'ACTIVE',
    }),
    __metadata("design:type", String)
], Users.prototype, "accountStatus", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', { name: 'phone_number', nullable: true }),
    __metadata("design:type", String)
], Users.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)('character varying', { name: 'two_factor_authentication_secret', nullable: true }),
    __metadata("design:type", String)
], Users.prototype, "twoFactorAuthenticationSecret", void 0);
__decorate([
    (0, typeorm_1.Column)('boolean', { name: 'is_two_factor_authentication_enabled', default: false }),
    __metadata("design:type", Boolean)
], Users.prototype, "isTwoFactorAuthenticationEnabled", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => Blogger_1.Blogger, (blogger) => blogger.blogger),
    __metadata("design:type", Blogger_1.Blogger)
], Users.prototype, "blogger", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => News_1.News, (news) => news.author),
    __metadata("design:type", Array)
], Users.prototype, "news", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => Seller_1.Seller, (seller) => seller.seller),
    __metadata("design:type", Seller_1.Seller)
], Users.prototype, "seller", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => WishList_1.WishList, wishList => wishList.user),
    __metadata("design:type", Array)
], Users.prototype, "wishLists", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => Home_1.Home, (home) => home.users),
    __metadata("design:type", Array)
], Users.prototype, "homes", void 0);
exports.Users = Users = __decorate([
    (0, typeorm_1.Entity)('users', { schema: 'public' })
], Users);
//# sourceMappingURL=Users.js.map