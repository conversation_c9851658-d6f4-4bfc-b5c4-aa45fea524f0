'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Button } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css'
import login from '@/styles/login.module.css'
import NotifyModal from '@/components/NotifyModal';

import { loginUser, redirectToGoogleLogin, redirectToFacebookLogin }
    from '@/app/api/auth/service/authApi';
import Link from 'next/link';
import { ERROR_MESSAGES } from '@/utils/error.messages';

export default function LoginPage() {
    const router = useRouter();

    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            router.push('/home');
        }
    }, []);

    const [modalState, setModalState] = useState<{
        show: boolean;
        message: string;
        onClose?: () => void;
    }>({
        show: false,
        message: '',
    })

    const openModal = (message: string, onClose?: () => void) => {
        setModalState({ show: true, message, onClose });
    }

    const handleCloseModal = () => {
        setModalState((prev) => {
            const onClose = prev.onClose;
            setTimeout(() => {
                if (onClose) onClose();
            }, 0);
            return { show: false, message: '' };
        });
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        const formData = new FormData(e.currentTarget);

        const username = formData.get('username') as string;
        const password = formData.get('password') as string;
        try {
            const response = await loginUser({ username, password });
            const { token, user } = response.data;

            localStorage.setItem('token', token);
            localStorage.setItem('user', JSON.stringify(user));

            router.push('/home');
        }
        catch (err: any) {
            let errorMsg = 'Đăng nhập thất bại';
            const backendMsg = err?.response?.data?.message;
            if (Array.isArray(backendMsg)) {
                const { message: errCode } = backendMsg[0] || {};
                if (errCode) {
                    errorMsg = ERROR_MESSAGES[errCode] || errCode;
                }
            }
            openModal('Lỗi: ' + errorMsg);
        }
    }
    return (
        <div className={`${login.background} ${login.fullHeight}`}>
            <div className={login.container}>
                <div className={`row g-0 ${login.fullHeight} ${login.row}`}>
                    <div className={`col-md-6 d-none d-xl-block ${login.fullHeight} ${login.containerLeft}`}>
                        <img src="/img/login-landmark.jpg" alt="Photo" />
                    </div>
                    <div className={`col-12 col-xl-6 ${login.fullHeight} ${login.containerRight}`}>
                        <div className={login.avatarLogo}>
                            <p>
                                KH
                            </p>
                        </div>
                        <p className={login.title}>
                            Chào mừng đến với Nền Tảng<br />
                            Bất Động Sản!
                        </p>
                        <p className={login.subtitle}>
                            Hãy đăng nhập để bắt đầu.
                        </p>
                        <form className={`${login.inputForm}`} onSubmit={handleSubmit}>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="username">
                                    Tên đăng nhập (Email hoặc Số điện thoại)
                                </label>
                                <input type="text" id="username" name="username" placeholder="Nhập Email hoặc Số điện thoại" required />
                            </div>
                            <div className={`${login.flexCol}`}>
                                <label htmlFor="password">
                                    Mật khẩu
                                </label>
                                <input type="password" id="password" name="password" placeholder="Nhập Mật Khẩu" required />

                            </div>
                            <div>
                                <p style={{ textAlign: 'left' }} className={login.miniTitle}>
                                    <Link href='/forgot-password'>
                                        Quên mật khẩu?
                                    </Link>
                                </p>
                            </div>
                            <div>
                                <p className={`${login.miniTitle}`}>
                                    Hoặc đăng nhập bằng
                                </p>
                            </div>
                            <div className={login.socialRow}>
                                <Button className={login.socialBtn} variant="outline-danger" onClick={redirectToGoogleLogin}>
                                    <i className="ti-google" style={{ marginRight: 8 }}></i>
                                    Google
                                </Button>
                                <Button className={login.socialBtn} variant="outline-primary" onClick={redirectToFacebookLogin}>
                                    <i className="ti-facebook" style={{ marginRight: 8 }}></i>
                                    Facebook
                                </Button>
                            </div>
                            <div>
                                <Button className={`${login.registerBtn}`} type="submit">
                                    Đăng nhập
                                </Button>
                            </div>
                            <div>
                                <p className={`${login.miniTitle}`}>
                                    Bạn chưa có tài khoản?
                                    <Link href='/signup'>
                                        Đăng ký
                                    </Link>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div >
            <NotifyModal show={modalState.show} message={modalState.message} onClose={handleCloseModal} />
        </div >
    )
}
