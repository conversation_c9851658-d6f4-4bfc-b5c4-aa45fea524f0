import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
} from "typeorm";
import { News } from "../news/News";
import { Users } from "../users/Users";

@Index("idx_blogger_blog_id", ["blogId"], {})
@Entity("blogger", { schema: "public" })
export class Blogger {
  @Column("uuid", { primary: true, name: "blogger_id" })
  bloggerId!: string;

  @Column("uuid", { name: "blog_id", nullable: true })
  blogId!: string | null;

  @ManyToOne(() => News, (news) => news.bloggers, { onDelete: "CASCADE" })
  @JoinColumn([{ name: "blog_id", referencedColumnName: "newsId" }])
  blog!: News;

  @OneToOne(() => Users, (users) => users.blogger, { onDelete: "CASCADE" })
  @JoinColumn([{ name: "blogger_id", referencedColumnName: "userId" }])
  blogger!: Users;
}
