.fullHeight {
    min-height: calc(var(--vh, 1vh) * 100);
}

.background {
    background-color: lightblue;
    min-width       : 100vw;
    display         : flex;
    align-items     : stretch;
    overflow        : auto;
}

.row {
    display    : flex;
    align-items: stretch;
    /* row không set height/min-height để tự động theo nội dung lớn nhất */
}

.container {
    width    : 90%;
    max-width: 1200px;
    margin   : 0 auto;
}

.flexRow {
    display    : flex;
    align-items: stretch;
    height     : 100%;
}

.containerLeft,
.containerRight {
    display       : flex;
    flex-direction: column;
}

.containerLeft {
    flex    : 1 1 0;
    overflow: hidden;
    position: relative;
}

.containerRight {
    flex            : 1 1 0;
    background-color: #fff;
    box-shadow      : 5px 0 8px rgba(0, 0, 0, 0.3);
    display         : flex;
    flex-direction  : column;
    align-items     : center;
    justify-content : center;
}

.containerLeft img {
    width          : 100%;
    height         : 100%;
    min-height     : 100%;
    max-height     : none;
    object-fit     : cover;
    object-position: top;
    position       : absolute;
    top            : 0;
    left           : 0;
}

.avatar<PERSON>ogo {
    width          : 48px;
    height         : 48px;
    background     : #636AE8;
    border-radius  : 9999px;
    display        : flex;
    align-items    : center;
    justify-content: center;
    margin-top     : 30px;
}

.avatarLogo p {
    font-size  : 20px;
    font-weight: 700;
    line-height: 28px;
    color      : #ffffff;
    margin     : 0;
}

.title {
    text-align : center;
    font-size  : 24px;
    font-weight: 700;
    line-height: 32px;
    color      : #242524;
    margin-top : 10px;
}

.subtitle {
    text-align : center;
    font-size  : 16px;
    font-weight: 400;
    line-height: 24px;
    color      : #8C8D8B;
    margin-top : -10px;
}

.inputForm {
    width    : 100%;
    max-width: 400px;
}

.nameInput input {
    width    : 100%;
    max-width: 100%;
}

.flexCol {
    display       : flex;
    flex-direction: column;
}

.inputForm label {
    font-size    : 14px;
    font-weight  : 500;
    margin-bottom: 5px;
    color        : #242524;
    margin-top   : 15px;
}

.inputForm input {
    padding      : 10px 12px;
    border       : 1px solid #ccc;
    border-radius: 6px;
    font-size    : 16px;
}

.verifyCheckbox {
    display    : flex;
    align-items: flex-start;
    gap        : 8px;
    cursor     : pointer;
}

.verifyCheckbox input {
    width      : 16px;
    height     : 16px;
    margin-top : 3px;
    flex-shrink: 0;
}

.miniAlert {
    color: #ff0000;
}

.verifyCheckbox span {
    font-size  : 14px;
    line-height: 1.5;
    font-weight: 500;
    color      : #242524;
}

.miniTitle {
    text-align   : center;
    font-size    : 12px;
    font-weight  : 400;
    line-height  : 34px;
    color        : #8C8D8B;
    margin-bottom: 6px;
}

.socialRow {
    display        : flex;
    flex-direction : row;
    justify-content: center;
    gap            : 12px;
    margin-bottom  : 15px;
}

.socialBtn {
    height         : 44px;
    justify-content: center;
    align-items    : center;
    font-weight    : 500;
    flex           : 1;
}

.registerBtn {
    width      : 100%;
    background : #636AE8;
    color      : #fff;
    font-weight: 500;
}

.authenticatorNumber {
    padding: 20px;
}

html,
body {
    height: 100%;
}

body {
    overflow-y: auto;
}

@media (max-width: 1199.98px) {
    .containerRight {
        box-shadow: 5px 0 8px rgba(0, 0, 0, 0.3), -5px 0 8px rgba(0, 0, 0, 0.3);
    }
}

@media (max-width: 767.98px) {
    .containerRight {
        padding: 0px 30px !important;
    }

    .container {
        width: 100%;
    }
}