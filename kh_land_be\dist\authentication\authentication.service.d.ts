import { UsersService } from '../users/users.service';
import { Users } from '../users/Users';
import { JwtService } from '@nestjs/jwt';
import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
export declare class AuthenticationService {
    private usersService;
    private jwtService;
    constructor(usersService: UsersService, jwtService: JwtService);
    validateUser(email: string, pass: string): Promise<Partial<Users> | null>;
    login(userWithoutPsw: Partial<Users>): Promise<{
        email: string | undefined;
        access_token: string;
    }>;
    loginWith2fa(userWithoutPsw: Partial<Users>): Promise<{
        email: string | undefined;
        access_token: string;
    }>;
    generateTwoFactorAuthenticationSecret(user: Users): Promise<{
        secret: string;
        otpauthUrl: string;
    }>;
    generateQrCodeDataURL(otpAuthUrl: string): Promise<string>;
    validateSocialLogin(socialUser: any): Promise<any>;
    isTwoFactorAuthenticationCodeValid(twoFactorAuthenticationCode: string, user: Users): boolean;
    verifyOtp(userId: string, otp: string): Promise<boolean>;
    findByEmail(email: string): Promise<Users | null>;
    createOAuthUser(data: any): Promise<Users>;
}
declare const Jwt2faStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class Jwt2faStrategy extends Jwt2faStrategy_base {
    private readonly userService;
    constructor(userService: UsersService, configService: ConfigService);
    validate(payload: any): Promise<Users>;
}
export {};
