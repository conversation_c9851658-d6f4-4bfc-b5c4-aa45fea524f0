"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleStrategy = void 0;
const passport_1 = require("@nestjs/passport");
const passport_google_oauth20_1 = require("passport-google-oauth20");
const common_1 = require("@nestjs/common");
const authentication_service_1 = require("../authentication.service");
let GoogleStrategy = class GoogleStrategy extends (0, passport_1.PassportStrategy)(passport_google_oauth20_1.Strategy, 'google') {
    constructor(authenticationService) {
        super({
            clientID: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            callbackURL: process.env.GOOGLE_CALLBACK_URL,
            scope: ['email', 'profile'],
        });
        this.authenticationService = authenticationService;
    }
    async validate(accessToken, refreshToken, profile) {
        try {
            const { name, emails, photos } = profile;
            const email = emails?.[0]?.value;
            if (!email) {
                throw new common_1.UnauthorizedException('No email found in Google profile');
            }
            let user = await this.authenticationService.findByEmail(email);
            if (!user) {
                user = await this.authenticationService.createOAuthUser({
                    email,
                    firstName: name?.givenName || 'Unknown',
                    lastName: name?.familyName || 'User',
                    avatarUrl: photos?.[0]?.value,
                    provider: 'google',
                    providerId: profile.id,
                });
            }
            return user;
        }
        catch (error) {
            console.error('Google OAuth validation error:', error);
            throw new common_1.UnauthorizedException('Failed to authenticate with Google');
        }
    }
};
exports.GoogleStrategy = GoogleStrategy;
exports.GoogleStrategy = GoogleStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService])
], GoogleStrategy);
//# sourceMappingURL=google.strategy.js.map