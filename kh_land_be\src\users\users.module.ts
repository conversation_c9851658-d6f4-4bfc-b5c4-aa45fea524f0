import { Module } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Users } from './Users'; // <PERSON><PERSON><PERSON> bảo đúng đường dẫn entity
import { JwtModule } from '@nestjs/jwt';
import { AuthenticationModule } from '../authentication/authentication.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Users]),
    JwtModule, // Thêm dòng này
    AuthenticationModule
    // ...các module khác nếu có
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
