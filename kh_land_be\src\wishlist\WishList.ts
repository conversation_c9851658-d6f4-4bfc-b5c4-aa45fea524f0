import { Column, Entity, Index, JoinColumn, ManyToOne } from "typeorm";
import { Users } from "../users/Users";

@Index("idx_wish_list_home_id", ["homeId"], {})
@Index("idx_wish_list_user_id", ["userId"], {})
@Entity("wish_list", { schema: "public" })
export class WishList {
  @Column("uuid", { primary: true, name: "home_id" })
  homeId!: string;

  @Column("uuid", { primary: true, name: "user_id" })
  userId!: string;

  @ManyToOne(() => Users, (users) => users.wishLists)
  @JoinColumn([{ name: "user_id", referencedColumnName: "userId" }])
  user!: Users;
}
