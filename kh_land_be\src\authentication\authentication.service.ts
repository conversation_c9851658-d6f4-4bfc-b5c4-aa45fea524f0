import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { Users } from '../users/Users';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable as Injectable2, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthenticationService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, pass: string): Promise<Partial<Users> | null> {
    const user = await this.usersService.getUserByEmail(email) as (Users & { password: string }) | null;
    if (!user || !user.password) return null;

    const isMatch = await bcrypt.compare(pass, user.password);
    if (isMatch) {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    return null;
  }

  async login(userWithoutPsw: Partial<Users>) {
    const payload = {
      sub: userWithoutPsw.userId,
      email: userWithoutPsw.email,
    };

    return {
      email: payload.email,
      access_token: this.jwtService.sign(payload),
    };
  }

  async loginWith2fa(userWithoutPsw: Partial<Users>) {
    const payload = {
      email: userWithoutPsw.email,
      isTwoFactorAuthenticationEnabled: !!userWithoutPsw.isTwoFactorAuthenticationEnabled,
      isTwoFactorAuthenticated: true,
    };

    return {
      email: payload.email,
      access_token: this.jwtService.sign(payload),
    };
  }

  async generateTwoFactorAuthenticationSecret(user: Users) {
    const secret = authenticator.generateSecret();
    const appName = process.env.AUTH_APP_NAME || 'KH Land App';
    
    const otpauthUrl = authenticator.keyuri(
      user.email,
      appName,
      secret,
    );

    // Log the actual otpauthUrl for testing
    console.log('otpauthUrl for testing:', otpauthUrl);
    
    await this.usersService.setTwoFactorAuthenticationSecret(secret, user.userId);

    return {
      secret,
      otpauthUrl,
    };
  }

  async generateQrCodeDataURL(otpAuthUrl: string): Promise<string> {
    return toDataURL(otpAuthUrl);
  }

  async validateSocialLogin(socialUser: any): Promise<any> {
    // Tìm hoặc tạo user trong DB dựa trên thông tin social
    let user = await this.usersService.findOrCreateSocialUser(socialUser);

    // Nếu user đã bật 2FA, trả về trạng thái yêu cầu xác thực OTP
    if (user.twoFactorAuthenticationSecret) {
      return { require2FA: true, userId: user.userId };
    }

    // Nếu chưa bật 2FA, trả về JWT và thông tin user
    const payload = { sub: user.userId, email: user.email };
    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  isTwoFactorAuthenticationCodeValid(twoFactorAuthenticationCode: string, user: Users): boolean {
    if (!user.twoFactorAuthenticationSecret) return false;
    return authenticator.verify({
      token: twoFactorAuthenticationCode,
      secret: user.twoFactorAuthenticationSecret,
    });
  }

  async verifyOtp(userId: string, otp: string): Promise<boolean> {
    const user = await this.usersService.getUserById(userId);
    if (!user || !user.twoFactorAuthenticationSecret) {
      throw new UnauthorizedException('User or 2FA secret not found');
    }
    const isValid = authenticator.verify({
      token: otp,
      secret: user.twoFactorAuthenticationSecret,
    });
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }
    return true;
  }

  async findByEmail(email: string) {
    return this.usersService.getUserByEmail(email);
  }

  async createOAuthUser(data: any) {
    return this.usersService.createUser(data);
  }
}

@Injectable2()
export class Jwt2faStrategy extends PassportStrategy(Strategy, 'jwt-2fa') {
  constructor(
    private readonly userService: UsersService,
    configService: ConfigService,
  ) {
    const jwtSecret = configService.get<string>('JWT_SECRET');
    if (!jwtSecret) {
      throw new Error('JWT_SECRET is not defined');
    }
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: jwtSecret,
    });
  }

  async validate(payload: any) {
    const user = await this.userService.getUserById(payload.sub);

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (!user.isTwoFactorAuthenticationEnabled) {
      return user;
    }
    if (payload.isTwoFactorAuthenticated) {
      return user;
    }
    throw new UnauthorizedException('2FA authentication required');
  }
}

// Remove this UsersService class declaration if UsersService is already defined and exported elsewhere in your project.
