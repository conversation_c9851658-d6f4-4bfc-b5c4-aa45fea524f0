import { Repository } from 'typeorm';
import { News } from './News';
import { AddressService } from '../address/address.service';
export declare class NewsService {
    private readonly newsRepository;
    private readonly addressService;
    constructor(newsRepository: Repository<News>, addressService: AddressService);
    createNews(newsData: Partial<News>): Promise<News>;
    getAllNews(): Promise<News[]>;
    getNewsById(newsId: string): Promise<News | null>;
    updateNewsById(newsId: string, updateNewsInfo: Partial<News>): Promise<News | null>;
    deleteNewsById(newsId: string): Promise<News | null>;
    getLatestNews(limit?: number): Promise<News[]>;
    getMostViewedNews(limit?: number): Promise<News[]>;
    getRecommendedNews(limit?: number): Promise<News[]>;
}
