"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "react-bootstrap";

export default function Profile() {
    const [user, setUser] = useState<any>(null);
    const router = useRouter();
    const [storageText, setStorageText] = useState('');

    useEffect(() => {
        const entries = Object.entries(localStorage)
            .map(([key, value]) => `${key}: ${value}`)
            .join('\n');
        setStorageText(entries);
        localStorage.removeItem("viewedKey");
        const token = localStorage.getItem("token");
        if (!token) {
            router.push("/login");
        }
        const userJson = localStorage.getItem("user");
        if (userJson) {
            setUser(JSON.parse(userJson));
        }
    }, []);

    const handleLogout = () => {
        localStorage.clear();
        router.push("/login");
    };

    if (!user) {
        return <p><PERSON>ạn cần đăng nhập để xem thông tin.</p>;
    }

    return (
        <div style={{ minHeight: "100vh", display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center" }}>
            <h1>Chào mừng, {user.name}</h1>
            <p>Email: {user.email}</p>
            <pre style={{ whiteSpace: 'pre-wrap', width: '100%', background: '#f4f4f4', padding: 12 }}>
                {storageText}
            </pre>
            <Button variant="danger" onClick={handleLogout} style={{ marginTop: 24 }}>
                Đăng xuất
            </Button>
        </div>
    );
}
