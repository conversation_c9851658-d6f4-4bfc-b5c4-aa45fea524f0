"use client";
import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function SocialCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get("token");
    const activeOtp = searchParams.get("activeOtp");
    if (token) {
      localStorage.setItem("accessToken", token);
      // Nếu user đã bật 2FA, chuy<PERSON><PERSON> sang xác minh OTP
      if (activeOtp === "1") {
        router.push("/verify-2FA");
      } else {
        // Nếu chưa bật 2FA, cho vào home/dashboard
        router.push("/home");
      }
    } else {
      router.push("/login");
    }
  }, [router, searchParams]);

  return <div><PERSON>ang kiểm tra xác thực...</div>;
}