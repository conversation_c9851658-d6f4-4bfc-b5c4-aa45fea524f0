"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatZodErrors = formatZodErrors;
const error_messages_1 = require("./error.messages");
function formatZodErrors(err) {
    return err.errors.map(e => {
        const err_code = e.message;
        const message = error_messages_1.ERROR_MESSAGES[err_code] || 'Invalid input';
        return {
            field: e.path.join('.') || 'unknown',
            message,
            err_code,
        };
    });
}
//# sourceMappingURL=zod.utils.js.map