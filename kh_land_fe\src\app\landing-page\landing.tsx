import React from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import { useRouter } from 'next/navigation';

export default function Landing() {
 const router = useRouter();
  return (
    <div style={{
  background: 'linear-gradient(90deg,rgba(240, 240, 240, 1) 0%, rgba(201, 201, 201, 1) 100%)',
  backdropFilter: 'blur(8px)',
  WebkitBackdropFilter: 'blur(8px)', // Safari
  padding: '2.5rem',
  maxWidth: '1024px',
  margin: '0 auto',
  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'

}}>
  <div className="row align-items-center">
    <div className="col-md-7">
      <h1 className="fw-bold mb-3">
        Tìm kiếm bất động sản <br /> mơ ước của bạn tại Việt Nam
      </h1>
      <p className="text-muted mb-4">
        N<PERSON><PERSON> tảng kết nối ng<PERSON>ờ<PERSON> mua, ng<PERSON><PERSON><PERSON> bán và chuyên gia bất động sản hàng đầu.
      </p>
      <div className="d-flex gap-2">
        <button className="btn btn-primary"  onClick={() => router.push('/signup')}>Đăng ký ngay</button>
        <button className="btn btn-outline-secondary">Tìm hiểu thêm</button>
      </div>
    </div>
    <div className="col-md-5 text-center">
      <img
        src="/img/login-landmark.jpg" 
        alt="Landmark"
        style={{ borderRadius: '12px', maxWidth: '100%', height: 'auto' }}
      />
    </div>
  </div>
</div>
  );
}