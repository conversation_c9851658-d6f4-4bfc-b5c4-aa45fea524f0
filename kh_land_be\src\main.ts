import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

import * as dotenv from 'dotenv';
import helmet from 'helmet';
import morgan from 'morgan';
import { json, urlencoded } from 'express';
import { Request, Response } from 'express';
import { ValidationPipe } from '@nestjs/common';


dotenv.config();

// Configure HTTP agents with longer timeouts for OAuth
// Set default timeout for HTTP requests
process.env.HTTP_TIMEOUT = '30000';
process.env.HTTPS_TIMEOUT = '30000';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  //   app.use(
  //     session({
  //       secret: process.env.SESSION_SECRET || 'your_session_secret',
  //       resave: false,
  //       saveUninitialized: false,
  //       cookie: { maxAge: 60 * 60 * 1000 }, // 1 giờ
  //     }),
  //   );

  //   app.enableCors({
  //     origin: ['http://localhost:3001', 'http://localhost:4200'], // Thay bằng domain frontend của bạn nếu cần
  //     credentials: true,
  //   });

  //   // Swagger config
  //   const config = new DocumentBuilder()
  //     .setTitle('API Documentation')
  //     .setDescription('NestJS API Swagger')
  //     .setVersion('1.0')
  //     .addBearerAuth() // Nếu dùng JWT
  //     .build();
  //   const document = SwaggerModule.createDocument(app, config);
  //   SwaggerModule.setup('api', app, document);

  //   await app.listen(3001);
  // }
  // void bootstrap();


  app.enableCors();

  app.use(json());
  app.use(urlencoded({ extended: true }));
  app.use(helmet());
  app.use(morgan('dev'));
  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));

  const expressApp = app.getHttpAdapter().getInstance();
  expressApp.get('/health', (_req: Request, res: Response) => res.status(200).send('Server is healthy'));

  const port = process.env.PORT || 3001;
  await app.listen(port);
  console.log(`🚀 Server is running on http://localhost:${port}`);
}

bootstrap();
