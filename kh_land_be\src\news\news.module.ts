import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { News } from "./News";
import { NewsService } from "./news.service";
import { NewsController } from "./news.controller";
import { AddressModule } from "src/address/address.module";

@Module({
    imports: [TypeOrmModule.forFeature([News]),
        AddressModule],
    controllers: [NewsController],
    providers: [NewsService],
    exports: [NewsService],
})
export class NewsModule { }