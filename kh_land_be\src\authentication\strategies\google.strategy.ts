import { PassportStrategy } from '@nestjs/passport';
import { Profile, Strategy } from 'passport-google-oauth20';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthenticationService } from '../authentication.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private authenticationService: AuthenticationService) {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: process.env.GOOGLE_CALLBACK_URL!,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile,
  ): Promise<any> {
    try {
      const { name, emails, photos } = profile;
      const email = emails?.[0]?.value;
      if (!email) {
        throw new UnauthorizedException('No email found in Google profile');
      }

      let user = await this.authenticationService.findByEmail(email);
      if (!user) {
        user = await this.authenticationService.createOAuthUser({
          email,
          firstName: name?.givenName || 'Unknown',
          lastName: name?.familyName || 'User',
          avatarUrl: photos?.[0]?.value,
          provider: 'google',
          providerId: profile.id,
        });
      }
      return user;
    } catch (error) {
      console.error('Google OAuth validation error:', error);
      throw new UnauthorizedException('Failed to authenticate with Google');
    }
  }
}

