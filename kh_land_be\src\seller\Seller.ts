import {
  Column,
  Entity,
  Index,
  JoinColumn,
  OneToMany,
  OneToOne,
} from "typeorm";
import { Home } from "../home/<USER>";
import { Users } from "../users/Users";


@Entity("seller", { schema: "public" })
export class Seller {
  @Column("uuid", { primary: true, name: "seller_id" })
  sellerId!: string;

  @OneToMany(() => Home, (home) => home.seller)
  homes!: Home[];

  @OneToOne(() => Users, (users) => users.seller, { onDelete: "CASCADE" })
  @JoinColumn([{ name: "seller_id", referencedColumnName: "userId" }])
  seller!: Users;
}
