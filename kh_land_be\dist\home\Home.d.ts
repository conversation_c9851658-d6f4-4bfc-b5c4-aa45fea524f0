import { Address } from "../address/Address";
import { HomeType } from "../home-type/HomeType";
import { Amenity } from "../amenity/Amenity";
import { HomePhoto } from "../home-photo/HomePhoto";
import { Seller } from "../seller/Seller";
import { Users } from "../users/Users";
export declare class Home {
    homeId: string;
    homeTypeId: string | null;
    price: string | null;
    addressId: string | null;
    squareFeet: number | null;
    contactInformation: string | null;
    homeName: string | null;
    views: number | null;
    priority: number | null;
    createdAt: Date | null;
    updatedAt: Date | null;
    deletedAt: Date | null;
    homeStatus: "FOR_SALE" | "FOR_RENT" | "UNAVAILABLE" | null;
    homeDescription: string | null;
    address: Address;
    homeType: HomeType;
    seller: Seller;
    amenities: Amenity[];
    homePhotos: HomePhoto[];
    users: Users[];
}
