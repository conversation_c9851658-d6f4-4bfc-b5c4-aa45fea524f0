import { IsEmail, <PERSON>Enum, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole } from '../../constant/constants';

export class UserDto {
  @ApiProperty()
  @IsString()
  id!: string;

  @ApiProperty()
  @IsString()
  firstName!: string;

  @ApiProperty()
  @IsString()
  lastName!: string;

  @ApiProperty()
  @IsEmail()
  email!: string;

  @ApiProperty()
  @IsString()
  phoneNumber!: string;

  @ApiProperty()
  @IsString()
  encryptedPassword!: string;

  @ApiProperty({ enum: UserRole, default: UserRole.USER })
  @IsEnum(UserRole)
  @IsOptional()
  role: UserRole = UserRole.USER;

  @ApiPropertyOptional({ type: String, nullable: true })
  @IsOptional()
  deletedAt?: Date | null;
}
