import { Controller, Get, Post, Req, UseGuards, Body, UnauthorizedException, BadRequestException, HttpCode } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthenticationService } from './authentication.service';
import { Request } from 'express';
import * as speakeasy from 'speakeasy';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { Users } from '../users/Users';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { Jwt2faAuthGuard } from './guards/jwt-2fa-auth.guard';

@Controller('auth')
export class AuthenticationController {
  constructor(
    private authenticationService: AuthenticationService,
    private jwtService: JwtService,
    private usersService: UsersService,
  ) {}

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleCallback(@Req() req: Request) {
    return this.authenticationService.validateSocialLogin(req.user);
  }

  @Get('facebook/callback')
  @UseGuards(AuthGuard('facebook'))
  async facebookCallback(@Req() req: Request) {
    return this.authenticationService.validateSocialLogin(req.user);
  }

  @Get('facebook')
  @UseGuards(AuthGuard('facebook'))
  async facebookAuth() {
    // This route initiates the Facebook OAuth flow
    // The actual logic is handled by Passport
  }

  @Post('2fa/verify')
  async verify2FA(@Body() body: { userId: string; otp: string }) {
    const { userId, otp } = body;
    if (!userId || !otp) {
      throw new BadRequestException('userId và otp là bắt buộc');
    }
    const user = await this.usersService.getUserById(userId);
    if (!user || !user.twoFactorAuthenticationSecret) {
      throw new UnauthorizedException('Không tìm thấy user hoặc chưa bật 2FA');
    }

    // Add debugging logs
    console.log('Verifying OTP:', {
      providedOtp: otp,
      userId: userId,
      secretLength: user.twoFactorAuthenticationSecret.length,
      // Don't log the full secret for security reasons
      secretPrefix: user.twoFactorAuthenticationSecret.substring(0, 5) + '...'
    });

    const isValid = speakeasy.totp.verify({
      secret: user.twoFactorAuthenticationSecret,
      encoding: 'base32',
      token: otp,
      window: 2  // Allow codes from 2 periods before and after (±60 seconds)
    });

    console.log('OTP verification result:', isValid);

    if (!isValid) throw new UnauthorizedException('OTP không đúng');
  
    // Bật 2FA cho user nếu xác thực thành công
    await this.usersService.turnOnTwoFactorAuthentication(userId);

    const payload = { sub: user.userId, email: user.email };
    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(200)
  async login(@Req() req: Request) {
    const userWithoutPsw: Partial<Users> = req.user as Users;
    return this.authenticationService.login(userWithoutPsw);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')  // This is a GET endpoint, not POST
  getProfile(@Req() req: Request) {
    return req.user;
  }

  @Post('2fa/turn-on')
  @UseGuards(JwtAuthGuard)
  async turnOnTwoFactorAuthentication(
    @Req() request: Request,
    @Body() body: { twoFactorAuthenticationCode: string }
  ) {
    if (!request.user) {
      throw new UnauthorizedException('User not found in request');
    }
    const user = request.user as Users;
    const isCodeValid =
      this.authenticationService.isTwoFactorAuthenticationCodeValid(
        body.twoFactorAuthenticationCode,
        user,
      );
    if (!isCodeValid) {
      throw new UnauthorizedException('Wrong authentication code');
    }
    await this.usersService.turnOnTwoFactorAuthentication(user.userId);
    return { message: 'Two-factor authentication enabled successfully' };
  }

  @Post('2fa/authenticate')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard)
  async authenticate(
    @Req() request: Request,
    @Body() body: { twoFactorAuthenticationCode: string }
  ) {
    const user = request.user as Users | undefined;
    if (!user) {
      throw new UnauthorizedException('User not found in request');
    }
    const isCodeValid = this.authenticationService.isTwoFactorAuthenticationCodeValid(
      body.twoFactorAuthenticationCode,
      user,
    );

    if (!isCodeValid) {
      throw new UnauthorizedException('Wrong authentication code');
    }

    return this.authenticationService.loginWith2fa(user);
  }

  @UseGuards(Jwt2faAuthGuard)
  @Get('protected-2fa')
  getProtectedResource(@Req() req: Request) {
    return req.user;
  }

  @Post('generate-2fa')
  @UseGuards(JwtAuthGuard)
  async generateTwoFactorAuthentication(@Req() request: Request) {
    const user = request.user as Users;
    if (!user) {
      throw new UnauthorizedException('User not found in request');
    }
    
    const { secret, otpauthUrl } = await this.authenticationService.generateTwoFactorAuthenticationSecret(user);
    const qrCodeDataUrl = await this.authenticationService.generateQrCodeDataURL(otpauthUrl);
    
    return {
      success: true,
      qr: qrCodeDataUrl,  // This is the base64 image data
      tempToken: secret
    };
  }

  @Post('debug-2fa-url')
  @UseGuards(JwtAuthGuard)
  async debugTwoFactorAuthenticationUrl(@Req() request: Request) {
    const user = request.user as Users;
    if (!user) {
      throw new UnauthorizedException('User not found in request');
    }
    
    const { secret, otpauthUrl } = await this.authenticationService.generateTwoFactorAuthenticationSecret(user);
    
    return {
      success: true,
      otpauthUrl,
      secret
    };
  }

  @Post('raw-2fa-url')
  @UseGuards(JwtAuthGuard)
  async getRawTwoFactorAuthenticationUrl(@Req() request: Request) {
    const user = request.user as Users;
    if (!user) {
      throw new UnauthorizedException('User not found in request');
    }
    
    const { secret, otpauthUrl } = await this.authenticationService.generateTwoFactorAuthenticationSecret(user);
    
    return {
      success: true,
      otpauthUrl,  // This is the raw URL string
      secret
    };
  }
}
