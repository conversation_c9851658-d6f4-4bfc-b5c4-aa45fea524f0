import { Modal, Button } from 'react-bootstrap';

type NotifyModalProps = {
    show: boolean;
    message: string;
    onClose: () => void;
};

export default function NotifyModal({ show, message, onClose }: NotifyModalProps) {
    return (
        <Modal show={show} onHide={onClose}>
            <Modal.Header closeButton>
                <Modal.Title>Thông báo</Modal.Title>
            </Modal.Header>
            <Modal.Body dangerouslySetInnerHTML={{ __html: message }} />
            <Modal.Footer>
                <Button variant="secondary" onClick={onClose}>
                    Đóng
                </Button>
            </Modal.Footer>
        </Modal>
    );
}
