import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Districts } from "./districts/Districts";
import { Address } from "../Address";

@Index("unique_ward_name_district", ["districtId", "wardName"], { unique: true })
@Index("idx_wards_district_id", ["districtId"], {})
@Entity("wards", { schema: "public" })
export class Wards {
  @PrimaryGeneratedColumn({ type: "integer", name: "ward_id" })
  wardId!: number;

  @Column("character varying", { name: "ward_name" })
  wardName!: string;

  @Column("integer", { name: "district_id" })
  districtId!: number;

  @OneToMany(() => Address, (address) => address.ward)
  addresses!: Address[];

  @ManyToOne(() => Districts, (districts) => districts.wards, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
  })
  @JoinColumn([{ name: "district_id", referencedColumnName: "districtId" }])
  district!: Districts;
}
