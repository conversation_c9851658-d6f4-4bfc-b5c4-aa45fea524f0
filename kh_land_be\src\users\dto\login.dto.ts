import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email hoặc số điện thoại',
  })
  @IsString()
  identifier!: string;

  @ApiProperty({ example: 'Password@123', description: 'Mật khẩu' })
  @IsString()
  password!: string;

  @ApiProperty({ example: '123456', description: 'Mã OTP' })
  @IsString()
  otp!: string;
}
