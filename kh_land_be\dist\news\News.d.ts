import { Address } from "../address/Address";
import { Users } from "../users/Users";
import { NewsPhoto } from "../news-photo/NewsPhoto";
import { Blogger } from "../blogger/Blogger";
export declare class News {
    newsId: string;
    title: string;
    content: string;
    publishedAt: string | null;
    authorId: string | null;
    source: string | null;
    addressId: string | null;
    priority: number | null;
    createdAt: Date | null;
    updatedAt: Date | null;
    deletedAt: Date | null;
    views: number | null;
    newsStatus: "ACTIVATE" | "BANNED" | "UNAVAILABLE" | null;
    bloggers: Blogger[];
    address: Address;
    author: Users;
    newsPhotos: NewsPhoto[];
}
