"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const Users_1 = require("./Users");
const jwt_1 = require("@nestjs/jwt");
let UsersService = class UsersService {
    constructor(userRepository, jwtService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
    }
    async createUser(userData) {
        const newUser = this.userRepository.create(userData);
        return this.userRepository.save(newUser);
    }
    async getAllUsers() {
        return this.userRepository.find({
            where: { deletedAt: (0, typeorm_2.IsNull)() },
        });
    }
    async getUserByEmail(email) {
        return this.userRepository.findOne({
            where: { email, deletedAt: (0, typeorm_2.IsNull)() },
        });
    }
    async getUserByPhoneNumber(phoneNumber) {
        return this.userRepository.findOne({
            where: { phoneNumber, deletedAt: (0, typeorm_2.IsNull)() },
        });
    }
    async getUserByEmailOrPhone(username) {
        return this.userRepository.findOne({
            where: [
                { email: username, deletedAt: (0, typeorm_2.IsNull)() },
                { phoneNumber: username, deletedAt: (0, typeorm_2.IsNull)() },
            ],
        });
    }
    async getUserById(userId) {
        return this.userRepository.findOne({
            where: { userId, deletedAt: (0, typeorm_2.IsNull)() },
        });
    }
    async updateUserById(userId, updateUserInfo) {
        await this.userRepository.update({ userId, deletedAt: (0, typeorm_2.IsNull)() }, updateUserInfo);
        return this.userRepository.findOne({ where: { userId, deletedAt: (0, typeorm_2.IsNull)() } });
    }
    async deleteUserById(userId) {
        await this.userRepository.softDelete({ userId });
        return this.userRepository.findOne({ where: { userId }, withDeleted: true });
    }
    async findOrCreateSocialUser(socialUser) {
        let user = await this.userRepository.findOne({
            where: { provider: socialUser.provider, providerId: socialUser.providerId },
        });
        if (!user) {
            user = this.userRepository.create({
                provider: socialUser.provider,
                providerId: socialUser.providerId,
                email: socialUser.email,
                firstName: socialUser.firstName,
                lastName: socialUser.lastName,
                avatarUrl: socialUser.avatarUrl,
            });
            await this.userRepository.save(user);
        }
        return user;
    }
    async setTwoFactorAuthenticationSecret(secret, userId) {
        await this.userRepository.update({ userId }, { twoFactorAuthenticationSecret: secret });
    }
    async turnOnTwoFactorAuthentication(userId) {
        await this.userRepository.update({ userId }, { isTwoFactorAuthenticationEnabled: true });
    }
    async login(dto) {
    }
    async verifyOtp(userId, otp) {
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(Users_1.Users)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService])
], UsersService);
//# sourceMappingURL=users.service.js.map