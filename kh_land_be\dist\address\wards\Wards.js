"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Wards = void 0;
const typeorm_1 = require("typeorm");
const Districts_1 = require("./districts/Districts");
const Address_1 = require("../Address");
let Wards = class Wards {
};
exports.Wards = Wards;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: "integer", name: "ward_id" }),
    __metadata("design:type", Number)
], Wards.prototype, "wardId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "ward_name" }),
    __metadata("design:type", String)
], Wards.prototype, "wardName", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "district_id" }),
    __metadata("design:type", Number)
], Wards.prototype, "districtId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Address_1.Address, (address) => address.ward),
    __metadata("design:type", Array)
], Wards.prototype, "addresses", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Districts_1.Districts, (districts) => districts.wards, {
        onDelete: "RESTRICT",
        onUpdate: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)([{ name: "district_id", referencedColumnName: "districtId" }]),
    __metadata("design:type", Districts_1.Districts)
], Wards.prototype, "district", void 0);
exports.Wards = Wards = __decorate([
    (0, typeorm_1.Index)("unique_ward_name_district", ["districtId", "wardName"], { unique: true }),
    (0, typeorm_1.Index)("idx_wards_district_id", ["districtId"], {}),
    (0, typeorm_1.Entity)("wards", { schema: "public" })
], Wards);
//# sourceMappingURL=Wards.js.map