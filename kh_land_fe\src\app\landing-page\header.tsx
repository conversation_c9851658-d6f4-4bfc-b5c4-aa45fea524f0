import React from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

export default function Header() {
 
  return (
    <nav>
    <div className="navbar navbar-expand-lg bg-white shadow-sm px-4 text-dark">
  <div className="container-fluid">
    <a className="navbar-brand fw-semibold" href="#">Nền Tảng Bất Động Sản</a>

    <div className="collapse navbar-collapse">
      <ul className="navbar-nav me-auto mb-2 mb-lg-0">
        <li className="nav-item">
          <a className="nav-link active text-primary fw-medium" href="#">Trang Chủ</a>
        </li>
        <li className="nav-item">
          <a className="nav-link text-dark" href="#">T<PERSON><PERSON></a>
        </li>
        <li className="nav-item">
          <a className="nav-link text-dark" href="#"><PERSON><PERSON><PERSON></a>
        </li>
      </ul>

      <div className="d-flex gap-2">
        <button className="btn btn-outline-secondary">Đăng ký</button>
        <button className="btn" style={{ backgroundColor: '#000000', color: '#fff' }}>Đăng nhập</button>
      </div>
    </div>
  </div>
</div>
</nav>
  );
}