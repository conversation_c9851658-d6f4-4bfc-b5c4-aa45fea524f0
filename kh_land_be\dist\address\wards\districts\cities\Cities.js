"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cities = void 0;
const typeorm_1 = require("typeorm");
const Districts_1 = require("../Districts");
const Countries_1 = require("./countries/Countries");
let Cities = class Cities {
};
exports.Cities = Cities;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: "integer", name: "city_id" }),
    __metadata("design:type", Number)
], Cities.prototype, "cityId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "city_name" }),
    __metadata("design:type", String)
], Cities.prototype, "cityName", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "country_id" }),
    __metadata("design:type", Number)
], Cities.prototype, "countryId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Countries_1.Countries, (countries) => countries.cities, {
        onDelete: "RESTRICT",
        onUpdate: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)([{ name: "country_id", referencedColumnName: "countryId" }]),
    __metadata("design:type", Countries_1.Countries)
], Cities.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Districts_1.Districts, (districts) => districts.city),
    __metadata("design:type", Array)
], Cities.prototype, "districts", void 0);
exports.Cities = Cities = __decorate([
    (0, typeorm_1.Index)("unique_city_name_country", ["countryId", "cityName"], { unique: true }),
    (0, typeorm_1.Index)("idx_cities_country_id", ["countryId"], {}),
    (0, typeorm_1.Entity)("cities", { schema: "public" })
], Cities);
//# sourceMappingURL=Cities.js.map