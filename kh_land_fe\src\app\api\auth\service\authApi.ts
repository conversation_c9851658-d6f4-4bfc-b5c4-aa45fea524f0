// src/app/api/auth/service/authApi.ts
import axios from 'axios';

export const API_BASE = 'http://localhost:3001';
export const api = axios.create({
    baseURL: API_BASE,
    headers: {
        'Content-Type': 'application/json',
    }
});

// Đăng ký tài kho<PERSON>n
export async function registerUser({ firstName, lastName, email, phoneNumber, password }: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    password: string;
}) {
    return api.post('/auth/signup', { firstName, lastName, email, phoneNumber, password });
}

// Đ<PERSON>ng nhập thường
export async function loginUser({ username, password }: { username: string; password: string }) {
    return api.post('/auth/login', { username, password });
}

// Quên mật khẩu
export async function forgotPassword({ username }: { username: string }) {
    return api.post('/auth/forgot-password', { username });
}

// Đ<PERSON><PERSON> mật khẩu qua link khôi phục
export async function resetPassword({ newPassword, token }: { newPassword: string; token: string | null }) {
    return api.post('/auth/reset-password', { newPassword }, { params: token ? { token } : {} });
};

// Xác thực OTP (sau khi login thường hoặc social)
export async function verifyOtp({ userId, otp }: { userId: string; otp: string }) {
    return api.post('/users/verify-otp', { userId, otp });
}

// Bật xác thực TOTP (lấy QR code)
export async function enableTotp({ userId }: { userId: string }) {
    return api.post('/users/enable-totp', { userId });
}

// Xác thực OTP từ ứng dụng Authenticator
export async function verifyTotp({ userId, otp }: { userId: string; otp: string }) {
    return api.post('/users/verify-totp', { userId, otp });
}

// Social login: chuyển hướng sang backend để login Google/Facebook
export function redirectToGoogleLogin() {
    window.location.href = API_BASE + '/auth/google';

}

export function redirectToFacebookLogin() {
    window.location.href = API_BASE + '/auth/facebook';
}
