import { HomeService } from 'src/home/<USER>';
import { NewsService } from 'src/news/news.service';
export declare class HomepageController {
    private readonly homeService;
    private readonly newsService;
    constructor(homeService: HomeService, newsService: NewsService);
    getHomepageData(): Promise<{
        homes: {
            mostViewed: import("../../home/<USER>").Home[];
            lastAdded: import("../../home/<USER>").Home[];
            recommended: import("../../home/<USER>").Home[];
            wishList: import("../../home/<USER>").Home[];
        };
        news: {
            latestNews: import("../../news/News").News[];
            mostViewedNews: import("../../news/News").News[];
            recommendedNews: import("../../news/News").News[];
        };
    }>;
    getHomeList(numberOfHomes?: number): Promise<{
        mostViewed: import("../../home/<USER>").Home[];
        lastAdded: import("../../home/<USER>").Home[];
        recommended: import("../../home/<USER>").Home[];
        wishList: import("../../home/<USER>").Home[];
    }>;
    getNewsList(numberOfNews?: number): Promise<{
        latestNews: import("../../news/News").News[];
        mostViewedNews: import("../../news/News").News[];
        recommendedNews: import("../../news/News").News[];
    }>;
}
