"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeType = void 0;
const typeorm_1 = require("typeorm");
const Home_1 = require("../home/<USER>");
let HomeType = class HomeType {
};
exports.HomeType = HomeType;
__decorate([
    (0, typeorm_1.Column)("uuid", {
        primary: true,
        name: "home_type_id",
        default: () => "uuid_generate_v4()",
    }),
    __metadata("design:type", String)
], HomeType.prototype, "homeTypeId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "home_type_name", nullable: true, length: 255 }),
    __metadata("design:type", Object)
], HomeType.prototype, "homeTypeName", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Home_1.Home, (home) => home.homeType),
    __metadata("design:type", Array)
], HomeType.prototype, "homes", void 0);
exports.HomeType = HomeType = __decorate([
    (0, typeorm_1.Entity)("home_type", { schema: "public" })
], HomeType);
//# sourceMappingURL=HomeType.js.map