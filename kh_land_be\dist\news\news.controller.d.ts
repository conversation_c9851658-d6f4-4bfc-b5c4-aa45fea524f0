import { NewsService } from './news.service';
export declare class NewsController {
    private readonly newsService;
    constructor(newsService: NewsService);
    getLatestNews(): Promise<import("./News").News[]>;
    getMostViewedNews(): Promise<import("./News").News[]>;
    getRecommendedNews(): Promise<import("./News").News[]>;
    getNewsById(newsId: string): Promise<import("./News").News | null>;
}
