"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Countries = void 0;
const typeorm_1 = require("typeorm");
const Cities_1 = require("../Cities");
let Countries = class Countries {
};
exports.Countries = Countries;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: "integer", name: "country_id" }),
    __metadata("design:type", Number)
], Countries.prototype, "countryId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "country_name", unique: true }),
    __metadata("design:type", String)
], Countries.prototype, "countryName", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Cities_1.Cities, (cities) => cities.country),
    __metadata("design:type", Array)
], Countries.prototype, "cities", void 0);
exports.Countries = Countries = __decorate([
    (0, typeorm_1.Index)("unique_country_name", ["countryName"], { unique: true }),
    (0, typeorm_1.Entity)("countries", { schema: "public" })
], Countries);
//# sourceMappingURL=Countries.js.map