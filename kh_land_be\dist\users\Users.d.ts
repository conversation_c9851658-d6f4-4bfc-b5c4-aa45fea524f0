import { News } from '../news/News';
import { Seller } from '../seller/Seller';
import { Home } from '../home/<USER>';
import { Blogger } from '../blogger/Blogger';
import { WishList } from '../wishlist/WishList';
export declare class Users {
    userId: string;
    provider: string;
    providerId: string;
    email: string;
    avatarUrl: string | null;
    role: 'USER' | 'ADMIN' | 'SELLER' | 'BLOGGER';
    createdAt: Date | null;
    updatedAt: Date | null;
    deletedAt: Date | null;
    otp: string | null;
    otpCreatedAt: Date | null;
    totpSecret: string | null;
    firstName: string | null;
    lastName: string | null;
    encryptedPassword: string | null;
    token: string | null;
    numberWrongPassword: number;
    accountStatus: 'ACTIVE' | 'SUSPENDED';
    phoneNumber: string;
    twoFactorAuthenticationSecret: string;
    isTwoFactorAuthenticationEnabled: boolean;
    blogger: Blogger;
    news: News[];
    seller: Seller;
    wishLists: WishList[];
    homes: Home[];
}
