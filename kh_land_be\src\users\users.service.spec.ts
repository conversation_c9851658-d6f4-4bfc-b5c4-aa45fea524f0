// filepath: d:\OJT\BDS1\kh_land_be\src\users\users.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Users } from './Users';
import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { UserRole } from '../constant/constants';
import * as bcrypt from 'bcrypt';

describe('UsersService', () => {
  let service: UsersService;
  let repo: Repository<Users>;
  let jwtService: JwtService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        JwtService,
        {
          provide: getRepositoryToken(Users),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repo = module.get<Repository<Users>>(getRepositoryToken(Users));
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should hash password when creating user', async () => {
    const userData = {
      firstName: 'A',
      lastName: 'B',
      email: '<EMAIL>',
      encryptedPassword: 'Password@123',
      role: UserRole.USER,
    };
    const hashed = await bcrypt.hash(userData.encryptedPassword, 10);
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    jest.spyOn(repo, 'create').mockImplementation((data) => data as any);
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    jest.spyOn(repo, 'save').mockResolvedValue({
      ...userData,
      encryptedPassword: hashed,
    } as any);

    const user = await service.createUser(userData);
    expect(
      await bcrypt.compare(userData.encryptedPassword, user.encryptedPassword),
    ).toBe(true);
  });

  it('should verify OTP successfully', async () => {
    const user = {
      id: '1',
      otp: '123456',
      otpCreatedAt: new Date(),
      email: '<EMAIL>',
      role: 'USER',
    } as Users;

    jest.spyOn(repo, 'findOne').mockResolvedValue(user);
    jest.spyOn(jwtService, 'sign').mockReturnValue('testtoken');
    jest.spyOn(repo, 'save').mockResolvedValue(user);

    const result = await service.verifyOtp('1', '123456');
    expect(result.token).toBe('testtoken');
    expect(result.user).toBe(user);
  });
});
