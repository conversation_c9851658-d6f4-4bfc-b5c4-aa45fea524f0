import React from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import { useRouter } from 'next/navigation';

export default function Cover() {
 const router = useRouter();
  return (
    <div
  style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
  }}
>
    <section
  className="text-white py-5"
  style={{
    backgroundImage: "url('/img/computer.jpg')", // 👈 thay bằng ảnh thật của bạn
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundColor: '#636AE8',
    backgroundBlendMode: 'multiply',
    height: 366,
    width: 680,
  }}
>
  <div className="container text-center">
    <h2 className="fw-bold mb-3">Không bỏ lỡ cơ hội đầu tư nào!</h2>
    <p className="mb-4">
      Đ<PERSON><PERSON> ký nhận bản tin để cập nhật các dự án mới, tin tức thị trường và phân tích chuyên sâu.
    </p>

    <form className="d-flex justify-content-center flex-wrap gap-2">
      <input
        type="email"
        placeholder="Nhập email của bạn"
        className="form-control"
        style={{ maxWidth: '300px' }}
      />
      <button type="submit" className="btn btn-light text-primary fw-semibold" onClick={() => router.push('/signup')}>
        Đăng ký
      </button>
    </form>
  </div>
</section>
</div>
  );
}