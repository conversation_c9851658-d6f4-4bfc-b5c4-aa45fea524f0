'use client';

import React, { useEffect, useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import { useRouter } from 'next/navigation';
import axios from 'axios';

interface Property {
  homeId: string;
  homeName: string;
  price: string;
  squareFeet: number;
  bedroom?: number;
  bathroom?: number;
  homePhotos: { photoUrl: string }[];
  homeType: { homeTypeName: string };
  amenities: { amenityName: string }[];
  address: {
    ward?: {
      wardName: string;
      district?: {
        districtName: string;
        city?: {
          cityName: string;
        };
      };
    };
  };
}

export default function SearchResults() {
  const router = useRouter();

  const [sortOption, setSortOption] = useState('price-desc');
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    homeTypeId: '',
    homeTypeName: '',
    priceMax: '',
    bedroomMin: '',     // ✅ ticked
    bedroomMax: '',     // ✅ ticked
    squareFeetMin: '',
    squareFeetMax: '',
    bathroomMin: '',
    bathroomMax: '',
    cityId: '',
    districtId: '',
    amenityId: '',
    searchKeyword: '',
  });
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12;

  const [homeTypes, setHomeTypes] = useState<{ homeTypeId: string; homeTypeName: string }[]>([]);

  useEffect(() => {
    const fetchHomeTypes = async () => {
      try {
        const res = await axios.get('http://localhost:3001/home-type');
        setHomeTypes(res.data);
      } catch (err) {
        console.error('Failed to fetch home types:', err);
      }
    };
    fetchHomeTypes();
  }, []);

  const fetchProperties = async () => {
    try {
      setLoading(true);
      const payload: any = {
        pageNumber: currentPage,
        ...Object.entries(filters)
          .filter(([_, value]) => value !== '')
          .reduce(
            (acc, [key, value]) => ({
              ...acc,
              [key]: isNaN(Number(value)) ? value : Number(value),
            }),
            {}
          ),
      };
      console.log(payload);
      const response = await axios.post('http://localhost:3001/find', payload);
      setProperties(response.data);
    } catch (error) {
      console.error('Fetch error:', error);
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProperties();
  }, [filters, currentPage]);

  const sorted = [...properties].sort((a, b) => {
    if (sortOption === 'price-desc') return Number(b.price) - Number(a.price);
    if (sortOption === 'price-asc') return Number(a.price) - Number(b.price);
    return 0;
  });

  const paginated = sorted.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  const totalPages = Math.ceil(sorted.length / pageSize);

  return (
    <div className="container py-4">
      <div className="card p-4 mb-4 shadow-sm border rounded">
        <h5 className="mb-3 fw-semibold">Bộ lọc tìm kiếm</h5>
        <div className="row g-3 align-items-end">
          <div className="col-md-4">
            <label className="form-label">Loại hình bất động sản</label>
            <select
              className="form-select"
              value={filters.homeTypeName}
              onChange={(e) => setFilters({ ...filters, homeTypeName: e.target.value })}
            >
              <option value="">Chọn loại</option>
              {homeTypes.map((type) => (
                <option key={type.homeTypeId} value={type.homeTypeName}>
                  {type.homeTypeName}
                </option>
              ))}
            </select>
          </div>

          <div className="col-md-4">
            <label className="form-label">Giá tối đa</label>
            <input
              className="form-control"
              type="number"
              placeholder="VD: 20000000"
              onChange={(e) => setFilters({ ...filters, priceMax: e.target.value })}
            />
          </div>

          {/* ✅ Bedroom filter changed */}
          <div className="col-md-4">
            <label className="form-label">Số phòng ngủ</label>
            <div className="input-group">
              <input
                className="form-control"
                type="number"
                placeholder="Tối thiểu"
                onChange={(e) => setFilters({ ...filters, bedroomMin: e.target.value })}
              />
              <input
                className="form-control"
                type="number"
                placeholder="Tối đa"
                onChange={(e) => setFilters({ ...filters, bedroomMax: e.target.value })}
              />
            </div>
          </div>
        </div>

        <div className="row g-3 mt-3">
          <div className="col-md-4">
            <label className="form-label">Diện tích (m²)</label>
            <div className="input-group">
              <input
                className="form-control"
                type="number"
                placeholder="Tối thiểu"
                onChange={(e) => setFilters({ ...filters, squareFeetMin: e.target.value })}
              />
              <input
                className="form-control"
                type="number"
                placeholder="Tối đa"
                onChange={(e) => setFilters({ ...filters, squareFeetMax: e.target.value })}
              />
            </div>
          </div>

          <div className="col-md-4">
            <label className="form-label">Số phòng tắm</label>
            <div className="input-group">
              <input
                className="form-control"
                type="number"
                placeholder="Tối thiểu"
                onChange={(e) => setFilters({ ...filters, bathroomMin: e.target.value })}
              />
              <input
                className="form-control"
                type="number"
                placeholder="Tối đa"
                onChange={(e) => setFilters({ ...filters, bathroomMax: e.target.value })}
              />
            </div>
          </div>

          <div className="col-md-4">
            <label className="form-label">Từ khóa tìm kiếm</label>
            <input
              className="form-control"
              type="text"
              placeholder="VD: hồ bơi, ban công"
              value={filters.searchKeyword}
              onChange={(e) => {
                setFilters({ ...filters, searchKeyword: e.target.value });
                setCurrentPage(1);
              }}
            />
          </div>
        </div>
      </div>

      {/* Sort & Summary */}
      <div className="d-flex justify-content-between align-items-center mb-3">
        <div>
          <strong>{properties.length}</strong> kết quả tìm kiếm
        </div>
        <div className="d-flex align-items-center gap-2">
          <button className="btn btn-outline-secondary">
            <img src="/Map.svg" alt="map" style={{ width: 18, height: 18, marginRight: '8px' }} />
            Xem bản đồ
          </button>
          <div className="d-flex align-items-center">
            <label className="me-2" style={{ width: 200 }}>↑↓ Sắp xếp theo:</label>
            <select className="form-select" value={sortOption} onChange={(e) => setSortOption(e.target.value)}>
              <option value="price-desc">Giá giảm dần</option>
              <option value="price-asc">Giá tăng dần</option>
            </select>
          </div>
        </div>
      </div>

      <hr className="my-3 w-50" />

      {/* Results */}
      {loading ? (
        <p className="text-muted">Loading...</p>
      ) : (
        <div className="row g-4">
          {paginated.map((property, index) => (
            <div className="col-md-4" key={index}>
              <div
                className="card shadow-sm h-100"
                style={{ cursor: 'pointer' }} // ✅ thêm để user biết có thể click
                onClick={() => router.push(`/property-detail/${property.homeId}`)} // ✅ Ticker: chuyển hướng
              >
                <img
                  src={property.homePhotos?.[0]?.photoUrl || '/default.jpg'}
                  className="card-img-top"
                  alt={`Hình ảnh ${property.homeName}`}
                  style={{ height: 200, objectFit: 'cover' }}
                />
                <div className="card-body">
                  <h5 className="card-title">{property.homeName}</h5>
                  <p className="card-text text-muted d-flex align-items-center">
                    <img src="/MapPin.svg" alt="icon" style={{ width: 18, height: 18, marginRight: 8 }} />
                    {[
                      property.address?.ward?.wardName,
                      property.address?.ward?.district?.districtName,
                      property.address?.ward?.district?.city?.cityName,
                    ].filter(Boolean).join(', ')}
                  </p>
                  <p className="card-text mb-1 d-flex align-items-center">
                    <img src="/Ruler.svg" alt="icon" style={{ width: 18, height: 18, marginRight: 8 }} />
                    {property.squareFeet} m²
                  </p>
                  <div className="d-flex justify-content-between align-items-center mb-1" style={{ gap: '2rem', width: '180px' }}>
                    <div className="d-flex align-items-center">
                      <img src="/BedDouble.svg" alt="bed" style={{ width: 18, height: 18, marginRight: 8 }} />
                      {property.bedroom ?? 'N/A'} PN
                    </div>
                    <div className="d-flex align-items-center">
                      <img src="/Bath.svg" alt="bath" style={{ width: 18, height: 18, marginRight: 8 }} />
                      {property.bathroom ?? 'N/A'} WC
                    </div>
                  </div>
                  <p className="card-text fw-bold text-primary">
                    {Number(property.price).toLocaleString()} VND
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      <div className="d-flex justify-content-center mt-4">
        {[...Array(totalPages)].map((_, i) => (
          <button
            key={i}
            className={`btn mx-1 ${i + 1 === currentPage ? 'btn-primary' : 'btn-outline-primary'}`}
            onClick={() => setCurrentPage(i + 1)}
          >
            {i + 1}
          </button>
        ))}
      </div>
    </div>
  );
}