"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacebookStrategy = void 0;
const passport_1 = require("@nestjs/passport");
const common_1 = require("@nestjs/common");
const passport_facebook_1 = require("passport-facebook");
const authentication_service_1 = require("../authentication.service");
let FacebookStrategy = class FacebookStrategy extends (0, passport_1.PassportStrategy)(passport_facebook_1.Strategy, 'facebook') {
    constructor(authenticationService) {
        const clientID = process.env.FACEBOOK_CLIENT_ID;
        const clientSecret = process.env.FACEBOOK_CLIENT_SECRET;
        const callbackURL = process.env.FACEBOOK_CALLBACK_URL || 'http://localhost:3001/auth/facebook/callback';
        if (!clientID || !clientSecret || !callbackURL) {
            throw new Error('Facebook OAuth environment variables are not set properly.');
        }
        super({
            clientID,
            clientSecret,
            callbackURL,
            profileFields: ['id', 'emails', 'name', 'displayName', 'photos'],
            scope: ['email'],
        });
        this.authenticationService = authenticationService;
    }
    async validate(accessToken, refreshToken, profile) {
        const { emails, displayName, photos, id } = profile;
        const user = {
            email: emails && emails[0]?.value,
            name: displayName,
            avatar: photos && photos[0]?.value,
            provider: 'facebook',
            providerId: id,
        };
        return user;
    }
};
exports.FacebookStrategy = FacebookStrategy;
exports.FacebookStrategy = FacebookStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService])
], FacebookStrategy);
//# sourceMappingURL=facebook.strategy.js.map