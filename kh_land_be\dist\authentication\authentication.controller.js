"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const authentication_service_1 = require("./authentication.service");
const speakeasy = __importStar(require("speakeasy"));
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const local_auth_guard_1 = require("./guards/local-auth.guard");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const jwt_2fa_auth_guard_1 = require("./guards/jwt-2fa-auth.guard");
let AuthenticationController = class AuthenticationController {
    constructor(authenticationService, jwtService, usersService) {
        this.authenticationService = authenticationService;
        this.jwtService = jwtService;
        this.usersService = usersService;
    }
    async googleCallback(req) {
        return this.authenticationService.validateSocialLogin(req.user);
    }
    async facebookCallback(req) {
        return this.authenticationService.validateSocialLogin(req.user);
    }
    async facebookAuth() {
    }
    async verify2FA(body) {
        const { userId, otp } = body;
        if (!userId || !otp) {
            throw new common_1.BadRequestException('userId và otp là bắt buộc');
        }
        const user = await this.usersService.getUserById(userId);
        if (!user || !user.twoFactorAuthenticationSecret) {
            throw new common_1.UnauthorizedException('Không tìm thấy user hoặc chưa bật 2FA');
        }
        console.log('Verifying OTP:', {
            providedOtp: otp,
            userId: userId,
            secretLength: user.twoFactorAuthenticationSecret.length,
            secretPrefix: user.twoFactorAuthenticationSecret.substring(0, 5) + '...'
        });
        const isValid = speakeasy.totp.verify({
            secret: user.twoFactorAuthenticationSecret,
            encoding: 'base32',
            token: otp,
            window: 2
        });
        console.log('OTP verification result:', isValid);
        if (!isValid)
            throw new common_1.UnauthorizedException('OTP không đúng');
        await this.usersService.turnOnTwoFactorAuthentication(userId);
        const payload = { sub: user.userId, email: user.email };
        return {
            access_token: this.jwtService.sign(payload),
            user,
        };
    }
    async login(req) {
        const userWithoutPsw = req.user;
        return this.authenticationService.login(userWithoutPsw);
    }
    getProfile(req) {
        return req.user;
    }
    async turnOnTwoFactorAuthentication(request, body) {
        if (!request.user) {
            throw new common_1.UnauthorizedException('User not found in request');
        }
        const user = request.user;
        const isCodeValid = this.authenticationService.isTwoFactorAuthenticationCodeValid(body.twoFactorAuthenticationCode, user);
        if (!isCodeValid) {
            throw new common_1.UnauthorizedException('Wrong authentication code');
        }
        await this.usersService.turnOnTwoFactorAuthentication(user.userId);
        return { message: 'Two-factor authentication enabled successfully' };
    }
    async authenticate(request, body) {
        const user = request.user;
        if (!user) {
            throw new common_1.UnauthorizedException('User not found in request');
        }
        const isCodeValid = this.authenticationService.isTwoFactorAuthenticationCodeValid(body.twoFactorAuthenticationCode, user);
        if (!isCodeValid) {
            throw new common_1.UnauthorizedException('Wrong authentication code');
        }
        return this.authenticationService.loginWith2fa(user);
    }
    getProtectedResource(req) {
        return req.user;
    }
    async generateTwoFactorAuthentication(request) {
        const user = request.user;
        if (!user) {
            throw new common_1.UnauthorizedException('User not found in request');
        }
        const { secret, otpauthUrl } = await this.authenticationService.generateTwoFactorAuthenticationSecret(user);
        const qrCodeDataUrl = await this.authenticationService.generateQrCodeDataURL(otpauthUrl);
        return {
            success: true,
            qr: qrCodeDataUrl,
            tempToken: secret
        };
    }
    async debugTwoFactorAuthenticationUrl(request) {
        const user = request.user;
        if (!user) {
            throw new common_1.UnauthorizedException('User not found in request');
        }
        const { secret, otpauthUrl } = await this.authenticationService.generateTwoFactorAuthenticationSecret(user);
        return {
            success: true,
            otpauthUrl,
            secret
        };
    }
    async getRawTwoFactorAuthenticationUrl(request) {
        const user = request.user;
        if (!user) {
            throw new common_1.UnauthorizedException('User not found in request');
        }
        const { secret, otpauthUrl } = await this.authenticationService.generateTwoFactorAuthenticationSecret(user);
        return {
            success: true,
            otpauthUrl,
            secret
        };
    }
};
exports.AuthenticationController = AuthenticationController;
__decorate([
    (0, common_1.Get)('google/callback'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('google')),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "googleCallback", null);
__decorate([
    (0, common_1.Get)('facebook/callback'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('facebook')),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "facebookCallback", null);
__decorate([
    (0, common_1.Get)('facebook'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('facebook')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "facebookAuth", null);
__decorate([
    (0, common_1.Post)('2fa/verify'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "verify2FA", null);
__decorate([
    (0, common_1.UseGuards)(local_auth_guard_1.LocalAuthGuard),
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(200),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "login", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AuthenticationController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('2fa/turn-on'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "turnOnTwoFactorAuthentication", null);
__decorate([
    (0, common_1.Post)('2fa/authenticate'),
    (0, common_1.HttpCode)(200),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "authenticate", null);
__decorate([
    (0, common_1.UseGuards)(jwt_2fa_auth_guard_1.Jwt2faAuthGuard),
    (0, common_1.Get)('protected-2fa'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AuthenticationController.prototype, "getProtectedResource", null);
__decorate([
    (0, common_1.Post)('generate-2fa'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "generateTwoFactorAuthentication", null);
__decorate([
    (0, common_1.Post)('debug-2fa-url'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "debugTwoFactorAuthenticationUrl", null);
__decorate([
    (0, common_1.Post)('raw-2fa-url'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthenticationController.prototype, "getRawTwoFactorAuthenticationUrl", null);
exports.AuthenticationController = AuthenticationController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [authentication_service_1.AuthenticationService,
        jwt_1.JwtService,
        users_service_1.UsersService])
], AuthenticationController);
//# sourceMappingURL=authentication.controller.js.map