"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const users_module_1 = require("./users/users.module");
const home_module_1 = require("./home/<USER>");
const news_module_1 = require("./news/news.module");
const homepage_module_1 = require("./pages/module/homepage.module");
const homeType_module_1 = require("./home-type/homeType.module");
const filterpage_module_1 = require("./pages/module/filterpage.module");
const typeorm_2 = require("typeorm");
const Users_1 = require("./users/Users");
const WishList_1 = require("./wishlist/WishList");
const Blogger_1 = require("./blogger/Blogger");
const Seller_1 = require("./seller/Seller");
const News_1 = require("./news/News");
const Home_1 = require("./home/<USER>");
const Address_1 = require("./address/Address");
const HomeType_1 = require("./home-type/HomeType");
const Amenity_1 = require("./amenity/Amenity");
const HomePhoto_1 = require("./home-photo/HomePhoto");
const Wards_1 = require("./address/wards/Wards");
const Districts_1 = require("./address/wards/districts/Districts");
const Cities_1 = require("./address/wards/districts/cities/Cities");
const Countries_1 = require("./address/wards/districts/cities/countries/Countries");
const NewsPhoto_1 = require("./news-photo/NewsPhoto");
let AppModule = class AppModule {
    constructor(dataSource) {
        this.dataSource = dataSource;
        console.log('Connected to DB:', this.dataSource.options.database);
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'postgres',
                host: process.env.DB_HOST,
                port: Number(process.env.DB_PORT),
                username: process.env.DB_USERNAME,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME,
                entities: [Users_1.Users, WishList_1.WishList, Blogger_1.Blogger, Seller_1.Seller, News_1.News, Home_1.Home, Address_1.Address, HomeType_1.HomeType, Amenity_1.Amenity, HomePhoto_1.HomePhoto, Wards_1.Wards, Districts_1.Districts, Cities_1.Cities, Countries_1.Countries, NewsPhoto_1.NewsPhoto],
                synchronize: true,
            }),
            users_module_1.UsersModule,
            home_module_1.HomeModule,
            news_module_1.NewsModule,
            homepage_module_1.HomepageModule,
            homeType_module_1.HomeTypeModule,
            filterpage_module_1.FilterPageModule,
            typeorm_1.TypeOrmModule.forFeature([Users_1.Users, WishList_1.WishList, Blogger_1.Blogger, Seller_1.Seller, News_1.News, Home_1.Home, Address_1.Address, HomeType_1.HomeType, Amenity_1.Amenity, HomePhoto_1.HomePhoto, Wards_1.Wards, Districts_1.Districts, Cities_1.Cities, Countries_1.Countries, NewsPhoto_1.NewsPhoto]),
        ],
        controllers: [],
    }),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], AppModule);
//# sourceMappingURL=app.module.js.map