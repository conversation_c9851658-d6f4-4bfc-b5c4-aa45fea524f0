"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsPhoto = void 0;
const typeorm_1 = require("typeorm");
const News_1 = require("../news/News");
let NewsPhoto = class NewsPhoto {
};
exports.NewsPhoto = NewsPhoto;
__decorate([
    (0, typeorm_1.Column)("uuid", {
        primary: true,
        name: "photo_id",
        default: () => "uuid_generate_v4()",
    }),
    __metadata("design:type", String)
], NewsPhoto.prototype, "photoId", void 0);
__decorate([
    (0, typeorm_1.Column)("uuid", { name: "news_id" }),
    __metadata("design:type", String)
], NewsPhoto.prototype, "newsId", void 0);
__decorate([
    (0, typeorm_1.Column)("text", { name: "photo_url" }),
    __metadata("design:type", String)
], NewsPhoto.prototype, "photoUrl", void 0);
__decorate([
    (0, typeorm_1.Column)("timestamp without time zone", {
        name: "uploaded_at",
        nullable: true,
        default: () => "now()",
    }),
    __metadata("design:type", Object)
], NewsPhoto.prototype, "uploadedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => News_1.News, (news) => news.newsPhotos),
    (0, typeorm_1.JoinColumn)([{ name: "news_id", referencedColumnName: "newsId" }]),
    __metadata("design:type", News_1.News)
], NewsPhoto.prototype, "news", void 0);
exports.NewsPhoto = NewsPhoto = __decorate([
    (0, typeorm_1.Index)("idx_news_photo_news_id", ["newsId"], {}),
    (0, typeorm_1.Entity)("news_photo", { schema: "public" })
], NewsPhoto);
//# sourceMappingURL=NewsPhoto.js.map