"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Districts = void 0;
const typeorm_1 = require("typeorm");
const Cities_1 = require("./cities/Cities");
const Wards_1 = require("../Wards");
let Districts = class Districts {
};
exports.Districts = Districts;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: "integer", name: "district_id" }),
    __metadata("design:type", Number)
], Districts.prototype, "districtId", void 0);
__decorate([
    (0, typeorm_1.Column)("character varying", { name: "district_name" }),
    __metadata("design:type", String)
], Districts.prototype, "districtName", void 0);
__decorate([
    (0, typeorm_1.Column)("integer", { name: "city_id" }),
    __metadata("design:type", Number)
], Districts.prototype, "cityId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Cities_1.Cities, (cities) => cities.districts, {
        onDelete: "RESTRICT",
        onUpdate: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)([{ name: "city_id", referencedColumnName: "cityId" }]),
    __metadata("design:type", Cities_1.Cities)
], Districts.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Wards_1.Wards, (wards) => wards.district),
    __metadata("design:type", Array)
], Districts.prototype, "wards", void 0);
exports.Districts = Districts = __decorate([
    (0, typeorm_1.Index)("unique_district_name_city", ["cityId", "districtName"], { unique: true }),
    (0, typeorm_1.Index)("idx_districts_city_id", ["cityId"], {}),
    (0, typeorm_1.Entity)("districts", { schema: "public" })
], Districts);
//# sourceMappingURL=Districts.js.map