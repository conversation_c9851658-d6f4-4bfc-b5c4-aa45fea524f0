import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Address } from "./Address";
import { AddressService } from "./address.service";
import { Wards } from "./wards/Wards";
import { Districts } from "./wards/districts/Districts";
import { Cities } from "./wards/districts/cities/Cities";
import { Countries } from "./wards/districts/cities/countries/Countries";

@Module({
    imports: [TypeOrmModule.forFeature([
        Address,
        Wards,
        Districts,
        Cities,
        Countries
    ]),
    ],
    providers: [AddressService],
    exports: [AddressService],
})
export class AddressModule { }
