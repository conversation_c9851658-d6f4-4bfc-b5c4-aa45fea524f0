"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function SocialCallbackPage() {
  const router = useRouter();

  useEffect(() => {
    // L<PERSON>y các tham số từ URL (userId, otpRequired, totpEnabled)
    const params = new URLSearchParams(window.location.search);
    const userId = params.get("userId");
    const otpRequired = params.get("otpRequired");
    const totpEnabled = params.get("totpEnabled");

    if (userId) {
      localStorage.setItem("userId", userId);
    }
    if (otpRequired) {
      localStorage.setItem("otpRequired", otpRequired);
    }
    if (totpEnabled) {
      localStorage.setItem("totpEnabled", totpEnabled);
    }

    // <PERSON><PERSON><PERSON><PERSON> hướng tiế<PERSON>, v<PERSON> <PERSON><PERSON> sang trang nhập OTP
    if (otpRequired === "true") {
      router.push("/verify-otp");
    } else {
      router.push("/home");
    }
  }, [router]);

  return <div>Đang xử lý đăng nhập social...</div>;
}
