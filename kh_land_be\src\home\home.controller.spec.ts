import { HomeController } from './home.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { HomeService } from './home.service';
import { NewsService } from '../news/news.service';
import { HomeDto } from './dto/home.dto';
import { NotFoundException } from '@nestjs/common';

describe('HomeController', () => {
    let controller: HomeController;
    let homeService: HomeService;
    let newsService: NewsService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [HomeController],
            providers: [
                {
                    provide: HomeService,
                    useValue: {
                        createHome: jest.fn(),
                        getAllHomes: jest.fn(),
                        getHomeById: jest.fn(),
                        updateHomeById: jest.fn(),
                        deleteHomeById: jest.fn(),
                        getHostPropertiesHome: jest.fn(),
                        getListHomeOrderBy: jest.fn(),
                        getMostViewedHomes: jest.fn(),
                        getLastAddedHomes: jest.fn(),
                        getRecommendedHomes: jest.fn(),
                    },
                },
                {
                    provide: NewsService,
                    useValue: {
                        getLatestNews: jest.fn().mockResolvedValue([]),
                        getMostViewedNews: jest.fn().mockResolvedValue([]),
                        getRecommendedNews: jest.fn().mockResolvedValue([]),
                    },
                }
            ],
        }).compile();

        controller = module.get<HomeController>(HomeController);
        homeService = module.get<HomeService>(HomeService);
        newsService = module.get<NewsService>(NewsService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    it('should call createHome', async () => {
        const dto = {
            homeName: 'Test Home',
            homeType: 'Apartment',
            address: {
                ward: 'Ward 1',
                district: 'District 1',
                cities: 'City 1',
                country: 'Country 1',
                addressId: '12345',
            },
            price: "100000",
            description: 'A beautiful test home',
            homeStatus: 'FOR_RENT',
            contactInformation: '123-456-7890',
            squareFeet: 1200,
            priority: 1,
            views: 10,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: null,
        };
        await controller.create(dto as HomeDto);
        // eslint-disable-next-line @typescript-eslint/unbound-method
        expect(homeService.createHome).toHaveBeenCalledWith(dto);
    });

    it('should call homeService and newsService functions in getAll()', async () => {
        await controller.getAll();

        expect(homeService.getMostViewedHomes).toHaveBeenCalled();
        expect(homeService.getLastAddedHomes).toHaveBeenCalled();
        expect(homeService.getRecommendedHomes).toHaveBeenCalled();
        expect(homeService.getHostPropertiesHome).toHaveBeenCalled();

        expect(newsService.getLatestNews).toHaveBeenCalled();
        expect(newsService.getMostViewedNews).toHaveBeenCalled();
        expect(newsService.getRecommendedNews).toHaveBeenCalled();
    });

    it('should call getListHomeOrderBy with correct params', async () => {
        const filter = 'views';
        await controller.getHomeByFilter(filter);
        expect(homeService.getListHomeOrderBy).toHaveBeenCalledWith(filter, 2);
    });

    it('should call findOne with correct params', async () => {
        const id = 'abc123';
        const updateData = { homeName: 'Find Home' };
        await controller.findOne(id);
        expect(homeService.getHomeById).toHaveBeenCalledWith(id);
    });

    it('should call updateHomeById with correct params', async () => {
        const id = 'abc123';
        const updateData = { homeName: 'Updated Home' };
        await controller.update(id, updateData as HomeDto);
        expect(homeService.updateHomeById).toHaveBeenCalledWith(id, updateData);
    });

    it('should call deleteHomeById with correct id', async () => {
        const id = 'abc123';
        await controller.remove(id);
        expect(homeService.deleteHomeById).toHaveBeenCalledWith(id);
    });

    it('should call getListHomeOrderBy with invalid key and throw NotFoundException', async () => {
        const filter = 'wrongFilter';

        (homeService.getListHomeOrderBy as jest.Mock).mockImplementation(() => {
            throw new NotFoundException([
                { field: 'filter', message: 'Err-022' }
            ]);
        });

        await expect(controller.getHomeByFilter(filter)).rejects.toThrow(NotFoundException);
        expect(homeService.getListHomeOrderBy).toHaveBeenCalledWith(filter, 2);
    });

    it('should call findOne with invalid key and throw NotFoundException', async () => {
        const id = 'wrongId';
        const updateData = { homeName: 'Find Home' };
        (homeService.getHomeById as jest.Mock).mockImplementation(() => {
            throw new NotFoundException([
                { field: 'homeId', message: 'Err-018' }
            ]);
        });
        await expect(controller.findOne(id)).rejects.toThrow(NotFoundException);
        expect(homeService.getHomeById).toHaveBeenCalledWith(id);
    });

    it('should call updateHomeById with  invalid key and throw NotFoundException', async () => {
        const id = 'wrongId';
        const updateData = { homeName: 'Updated Home' };
        (homeService.updateHomeById as jest.Mock).mockImplementation(() => {
            throw new NotFoundException([
                { field: 'homeId', message: 'Err-020' }
            ]);
        });
        await expect(controller.update(id, updateData as HomeDto)).rejects.toThrow(NotFoundException);
        expect(homeService.updateHomeById).toHaveBeenCalledWith(id, updateData);
    });

    it('should call deleteHomeById with  invalid key and throw NotFoundException', async () => {
        const id = 'wrongId';
        (homeService.deleteHomeById as jest.Mock).mockImplementation(() => {
            throw new NotFoundException([
                { field: 'homeId', message: 'Err-017' }
            ]);
        });
        await expect(controller.remove(id)).rejects.toThrow(NotFoundException);
        expect(homeService.deleteHomeById).toHaveBeenCalledWith(id);
    });
});
