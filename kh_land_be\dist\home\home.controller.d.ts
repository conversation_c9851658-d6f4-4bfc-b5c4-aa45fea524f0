import { HomeService } from "./home.service";
import { Home } from "./Home";
import { HomeDto } from "./dto/home.dto";
export declare class HomeController {
    private readonly homeService;
    constructor(homeService: HomeService);
    getAll(): Promise<Home[]>;
    getHomeByFilter(filter: string): Promise<Home[]>;
    findOne(homeId: string): Promise<Home | null>;
    create(body: HomeDto): Promise<Home>;
    update(homeId: string, body: Partial<Home>): Promise<void>;
    remove(homeId: string): Promise<Home | null>;
}
