import { Type } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsUUID, IsEnum, IsDate, IsArray, Min, Matches } from 'class-validator';

export class HomeDto {
    @IsString()
    @IsNotEmpty()
    homeName!: string;

    @IsString()
    @Matches(/^\d+$/)
    price!: string;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    squareFeet?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    priority?: number;

    @Type(() => Number)
    @IsNumber()
    @IsOptional()
    @Min(0)
    views?: number;

    @IsString()
    @IsOptional()
    contactInformation?: string;

    @IsEnum(["FOR_RENT", "FOR_SALE", "UNAVAILABLE"])
    homeStatus!: "FOR_RENT" | "FOR_SALE" | "UNAVAILABLE";
}
