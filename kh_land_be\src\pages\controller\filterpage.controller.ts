import { Body, Controller, NotFoundException, Post } from '@nestjs/common';
import { HomeService } from 'src/home/<USER>';
import { FilterPageDto } from '../dto/filter.dto';
import { ERROR_MESSAGES } from 'src/utils/error.messages';

@Controller('find')
export class FilterPageController {
    constructor(
        private readonly homeService: HomeService,
    ) { }
    @Post()
    async searchHome(@Body() searchParams: FilterPageDto) {
        const pageSize = 10;
        const home = await this.homeService.searchByFilter(searchParams, pageSize);
        if (!home) {
            throw new NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-017',
                    detail: ERROR_MESSAGES['Err-017']
                }
            ]);
        }
        return home;
    }
}