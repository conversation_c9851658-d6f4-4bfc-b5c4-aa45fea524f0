import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { HomeService } from "./home.service";
import { Home } from "./Home";
import { HomeDto } from "./dto/home.dto";


@Controller('home')
export class HomeController {
    constructor(private readonly homeService: HomeService) { }

    @Get()
    async getAll(): Promise<Home[]> {
        return this.homeService.getAllHomes();
    }

    @Get('filter/:filter')
    async getHomeByFilter(@Param('filter') filter: string) {
        const numberOfHomes = 1;
        if (filter === 'properties') {
            return this.homeService.getHostPropertiesHome(numberOfHomes);
        }
        return this.homeService.getListHomeOrderBy(filter as keyof Home, numberOfHomes);
    }

    @Get('home-id/:homeId')
    async findOne(@Param('homeId') homeId: string) {
        return this.homeService.getHomeById(homeId);
    }

    @Post()
    async create(@Body() body: HomeDto): Promise<Home> {
        return this.homeService.createHome(body);
    }

    @Put('home-update/:homeId')
    async update(
        @Param('homeId') homeId: string,
        @Body() body: Partial<Home>,
    ) {
        return this.homeService.updateHomeById(homeId, body);
    }

    @Delete('home-delete/:homeId')
    async remove(@Param('homeId') homeId: string) {
        return this.homeService.deleteHomeById(homeId);
    }
}