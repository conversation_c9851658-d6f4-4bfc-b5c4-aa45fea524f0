"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const Home_1 = require("./Home");
const home_controller_1 = require("./home.controller");
const home_service_1 = require("./home.service");
const address_module_1 = require("../address/address.module");
const news_module_1 = require("../news/news.module");
const Blogger_1 = require("../blogger/Blogger");
const HomePhoto_1 = require("../home-photo/HomePhoto");
const Amenity_1 = require("../amenity/Amenity");
const Address_1 = require("../address/Address");
const NewsPhoto_1 = require("../news-photo/NewsPhoto");
const News_1 = require("../news/News");
const Seller_1 = require("../seller/Seller");
let HomeModule = class HomeModule {
};
exports.HomeModule = HomeModule;
exports.HomeModule = HomeModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([Home_1.Home,
                Seller_1.Seller,
                News_1.News,
                NewsPhoto_1.NewsPhoto,
                Address_1.Address,
                Home_1.Home,
                Amenity_1.Amenity,
                HomePhoto_1.HomePhoto,
                Blogger_1.Blogger
            ]),
            address_module_1.AddressModule,
            news_module_1.NewsModule
        ],
        controllers: [home_controller_1.HomeController],
        providers: [home_service_1.HomeService],
        exports: [home_service_1.HomeService],
    })
], HomeModule);
//# sourceMappingURL=home.module.js.map