import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { Users } from './Users';
import { AuthenticationService } from '../authentication/authentication.service';
export declare class UsersController {
    private readonly usersService;
    private readonly authenticationService;
    constructor(usersService: UsersService, authenticationService: AuthenticationService);
    create(body: Partial<Users>): Promise<Users>;
    findAll(): Promise<Users[]>;
    findOne(userId: string): Promise<Users | null>;
    update(userId: string, body: Partial<Users>): Promise<Users | null>;
    remove(userId: string): Promise<Users | null>;
    login(dto: LoginDto): Promise<{
        email: string | undefined;
        access_token: string;
    }>;
    verifyOtp(body: {
        userId: string;
        otp: string;
    }): Promise<boolean>;
}
