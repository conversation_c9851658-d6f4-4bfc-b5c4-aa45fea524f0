import { Strategy, Profile } from 'passport-facebook';
import { AuthenticationService } from '../authentication.service';
declare const FacebookStrategy_base: new (...args: [options: import("passport-facebook").StrategyOptionsWithRequest] | [options: import("passport-facebook").StrategyOptions]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class FacebookStrategy extends FacebookStrategy_base {
    private authenticationService;
    constructor(authenticationService: AuthenticationService);
    validate(accessToken: string, refreshToken: string, profile: Profile): Promise<any>;
}
export {};
