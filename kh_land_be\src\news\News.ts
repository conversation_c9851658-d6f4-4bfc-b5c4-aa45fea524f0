import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Address } from "../address/Address";
import { Users } from "../users/Users";
import { NewsPhoto } from "../news-photo/NewsPhoto";
import { Blogger } from "../blogger/Blogger";

@Index("idx_news_address_id", ["addressId"], {})
@Index("idx_news_author_id", ["authorId"], {})
@Entity("news", { schema: "public" })
export class News {
  @Column("uuid", {
    primary: true,
    name: "news_id",
    default: () => "uuid_generate_v4()",
  })
  newsId!: string;

  @Column("character varying", { name: "title", length: 255 })
  title!: string;

  @Column("text", { name: "content" })
  content!: string;

  @Column("date", {
    name: "published_at",
    nullable: true,
    default: () => "CURRENT_DATE",
  })
  publishedAt!: string | null;

  @Column("uuid", { name: "author_id", nullable: true })
  authorId!: string | null;

  @Column("character varying", { name: "source", nullable: true, length: 255 })
  source!: string | null;

  @Column("uuid", { name: "address_id", nullable: true })
  addressId!: string | null;

  @Column("integer", { name: "priority", nullable: true, default: () => "1" })
  priority!: number | null;

  @Column("timestamp without time zone", {
    name: "created_at",
    nullable: true,
    default: () => "now()",
  })
  createdAt!: Date | null;

  @Column("timestamp without time zone", {
    name: "updated_at",
    nullable: true,
    default: () => "now()",
  })
  updatedAt!: Date | null;

  @Column("timestamp without time zone", { name: "deleted_at", nullable: true })
  deletedAt!: Date | null;

  @Column("integer", { name: "views", nullable: true, default: () => "0" })
  views!: number | null;

  @Column("enum", {
    name: "news_status",
    nullable: true,
    enum: ["ACTIVATE", "BANNED", "UNAVAILABLE"],
    default: "ACTIVATE", // Sửa default cho đúng chuẩn Postgres
  })
  newsStatus!: "ACTIVATE" | "BANNED" | "UNAVAILABLE" | null;

  @OneToMany(() => Blogger, (blogger) => blogger.blog)
  bloggers!: Blogger[];

  @ManyToOne(() => Address, (address) => address.news, {
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  })
  @JoinColumn([{ name: "address_id", referencedColumnName: "addressId" }])
  address!: Address;

  @ManyToOne(() => Users, (users) => users.news)
  @JoinColumn([{ name: "author_id", referencedColumnName: "userId" }])
  author!: Users;

  @OneToMany(() => NewsPhoto, (newsPhoto) => newsPhoto.news)
  newsPhotos!: NewsPhoto[];
}
