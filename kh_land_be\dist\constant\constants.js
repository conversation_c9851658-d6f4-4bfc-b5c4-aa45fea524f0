"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JWT_TOKEN_TYPE = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["MODERATOR"] = "MODERATOR";
    UserRole["USER"] = "USER";
})(UserRole || (exports.UserRole = UserRole = {}));
var JWT_TOKEN_TYPE;
(function (JWT_TOKEN_TYPE) {
    JWT_TOKEN_TYPE["ACCESS"] = "access";
    JWT_TOKEN_TYPE["REFRESH"] = "refresh";
    JWT_TOKEN_TYPE["RESET_PASSWORD"] = "reset_password";
})(JWT_TOKEN_TYPE || (exports.JWT_TOKEN_TYPE = JWT_TOKEN_TYPE = {}));
//# sourceMappingURL=constants.js.map