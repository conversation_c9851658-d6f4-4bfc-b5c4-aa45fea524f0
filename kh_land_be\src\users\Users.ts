import {
  Column,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { News } from '../news/News';
import { Seller } from '../seller/Seller';
import { Home } from '../home/<USER>';
import { Blogger } from '../blogger/Blogger';
import { WishList } from '../wishlist/WishList';


@Entity('users', { schema: 'public' })
export class Users {
  @Column('uuid', {
    primary: true,
    name: 'user_id',
    default: () => 'uuid_generate_v4()',
  })
  userId!: string;

  @Column({ name: 'provider', nullable: true })
  provider!: string; // 'google', 'facebook', 'local', ...

  @Column({ name: 'provider_id', nullable: true })
  providerId!: string; // ID từ Google/Facebook

  @Column({ name: 'email', nullable: true })
  email!: string;

  @Column('text', { name: 'avatar_url', nullable: true })
  avatarUrl!: string | null;

  @Column('enum', {
    name: 'role',
    enum: ['USER', 'ADMIN', 'SELLER', 'BLOGGER'],
    default: 'USER', // Sửa default cho đúng chuẩn Postgres
  })
  role!: 'USER' | 'ADMIN' | 'SELLER' | 'BLOGGER';

  @Column('timestamp without time zone', {
    name: 'created_at',
    nullable: true,
    default: () => 'now()',
  })
  createdAt!: Date | null;

  @Column('timestamp without time zone', {
    name: 'updated_at',
    nullable: true,
    default: () => 'now()',
  })
  updatedAt!: Date | null;

  @Column('timestamp without time zone', { name: 'deleted_at', nullable: true })
  deletedAt!: Date | null;

  @Column('character varying', { name: 'otp', nullable: true, length: 10 })
  otp!: string | null;

  @Column('timestamp without time zone', {
    name: 'otpCreatedAt',
    nullable: true,
  })
  otpCreatedAt!: Date | null;

  @Column('character varying', {
    name: 'totpSecret',
    nullable: true,
    length: 64,
  })
  totpSecret!: string | null;

  @Column('character varying', { name: 'first_name', length: 255, nullable: true })
  firstName!: string | null;

  @Column('character varying', { name: 'last_name', length: 255, nullable: true })
  lastName!: string | null;


  @Column('character varying', { name: 'encrypted_password', length: 255, nullable: true })
  encryptedPassword!: string | null;

  @Column('text', { name: 'token', nullable: true })
  token!: string | null;

  @Column('integer', { name: 'number_wrong_password', default: () => '0' })
  numberWrongPassword!: number;

  @Column('enum', {
    name: 'account_status',
    enum: ['ACTIVE', 'SUSPENDED'],
    default: 'ACTIVE', // Sửa default cho đúng chuẩn Postgres
  })
  accountStatus!: 'ACTIVE' | 'SUSPENDED';

  @Column('character varying', { name: 'phone_number', nullable: true })
  phoneNumber!: string;

  @Column('character varying', { name: 'two_factor_authentication_secret', nullable: true })
  twoFactorAuthenticationSecret!: string;

  @Column('boolean', { name: 'is_two_factor_authentication_enabled', default: false })
  isTwoFactorAuthenticationEnabled!: boolean;

  @OneToOne(() => Blogger, (blogger) => blogger.blogger)
  blogger!: Blogger;

  @OneToMany(() => News, (news) => news.author)
  news!: News[];

  @OneToOne(() => Seller, (seller) => seller.seller)
  seller!: Seller;

  @OneToMany(() => WishList, wishList => wishList.user)
  wishLists!: WishList[];

  @ManyToMany(() => Home, (home) => home.users)
  homes!: Home[];
}
