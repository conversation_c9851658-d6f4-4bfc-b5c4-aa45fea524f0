'use client';
import React, { useEffect, useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import axios from 'axios';
import { useRouter } from 'next/navigation';

interface Property {
  homeId: string;
  homeName: string;
  price: string;
  squareFeet: number;
  bedroom?: number;
  bathroom?: number;
  homePhotos: { photoUrl: string }[];
  homeType: { homeTypeName: string };
  amenities: { amenityName: string }[];
  address: {
    ward?: {
      wardName: string;
      district?: {
        districtName: string;
        city?: {
          cityName: string;
        };
      };
    };
  };
}

export default function Carousel() {
  const router = useRouter();
  const [propertyGroups, setPropertyGroups] = useState<Property[][]>([]);

  useEffect(() => {
    async function fetchProperties() {
      try {
        const res = await axios.get('http://localhost:3001/');
        const data = res.data;

        const allHomes: Property[] = [
          ...data.homes.mostViewed,
          ...data.homes.lastAdded,
          ...data.homes.recommended,
          ...data.homes.wishList,
        ];

        const grouped: Property[][] = [];
        for (let i = 0; i < allHomes.length; i += 3) {
          grouped.push(allHomes.slice(i, i + 3));
        }

        setPropertyGroups(grouped);
      } catch (error) {
        console.error('Failed to fetch properties:', error);
      }
    }

    fetchProperties();
  }, []);

  return (
    <div id="carouselProperty" className="carousel slide" data-bs-ride="carousel">
      <div className="carousel-inner">
        {propertyGroups.map((group, index) => (
          <div key={index} className={`carousel-item ${index === 0 ? 'active' : ''}`}>
            <div className="container">
              <div className="row">
                {group.map((property, i) => (
                  <div className="col-md-4" key={i}>
                    <div
                      className="card shadow-sm h-100"
                      style={{ cursor: 'pointer' }}
                      onClick={() => router.push(`/property-detail/${property.homeId}`)}
                    >
                      <img
                        src={property.homePhotos?.[0]?.photoUrl || '/default.jpg'}
                        className="card-img-top"
                        alt={`Hình ảnh ${property.homeName}`}
                        style={{ height: 200, objectFit: 'cover' }}
                      />
                      <div className="card-body">
                        <h5 className="card-title">{property.homeName}</h5>
                        <p className="card-text text-muted d-flex align-items-center">
                          <img src="/MapPin.svg" alt="icon" style={{ width: 18, height: 18, marginRight: 8 }} />
                          {[
                            property.address?.ward?.wardName,
                            property.address?.ward?.district?.districtName,
                            property.address?.ward?.district?.city?.cityName
                          ].filter(Boolean).join(', ')}
                        </p>
                        <p className="card-text mb-1 d-flex align-items-center">
                          <img src="/Ruler.svg" alt="icon" style={{ width: 18, height: 18, marginRight: 8 }} />
                          {property.squareFeet} m²
                        </p>
                        <div className="d-flex justify-content-between align-items-center mb-1" style={{ gap: '2rem', width: '180px' }}>
                          <div className="d-flex align-items-center">
                            <img src="/BedDouble.svg" alt="bed" style={{ width: 18, height: 18, marginRight: 8 }} />
                            {property.bedroom ?? 'N/A'} PN
                          </div>
                          <div className="d-flex align-items-center">
                            <img src="/Bath.svg" alt="bath" style={{ width: 18, height: 18, marginRight: 8 }} />
                            {property.bathroom ?? 'N/A'} WC
                          </div>
                        </div>
                        <p className="card-text fw-bold text-primary">{Number(property.price).toLocaleString()} VND</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      <button className="carousel-control-prev" type="button" data-bs-target="#carouselProperty" data-bs-slide="prev">
        <span className="carousel-control-prev-icon" aria-hidden="true" />
        <span className="visually-hidden">Previous</span>
      </button>
      <button className="carousel-control-next" type="button" data-bs-target="#carouselProperty" data-bs-slide="next">
        <span className="carousel-control-next-icon" aria-hidden="true" />
        <span className="visually-hidden">Next</span>
      </button>
    </div>
  );
}
