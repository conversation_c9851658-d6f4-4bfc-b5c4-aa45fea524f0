import { Address } from './../address/Address';
import { HomeType } from './../home-type/HomeType';
import { AddressService } from './../address/address.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Home } from './Home';
import { In, Repository, DataSource, Not } from 'typeorm';
import { HomeDto } from './dto/home.dto';
import { FilterPageDto } from 'src/pages/dto/filter.dto';
import { ERROR_MESSAGES } from '../utils/error.messages';
@Injectable()
export class HomeService {
    constructor(
        @InjectRepository(Home)
        private readonly homeRepository: Repository<Home>,
        private readonly dataSource: DataSource,
        private readonly addressService: AddressService,
    ) { }


    async getAllHomes(): Promise<Home[]> {
        const homes = await this.homeRepository.find({
            where: { homeStatus: Not('UNAVAILABLE') },
            relations: ['homeType', 'address'],
        });
        return homes;
    }

    async getHomeById(homeId: string): Promise<Home | null> {
        try {
            const home = await this.homeRepository.findOne({
                where: { homeId },
                relations: ['homeType', 'address', 'seller'],
            });
            if (!home) {
                throw new NotFoundException([
                    {
                        field: 'homeId',
                        message: 'Err-017',
                        detail: ERROR_MESSAGES['Err-017']
                    },
                ]);
            }
            home.address = await this.addressService.getAddressById(home.address.addressId);
            return home;
        } catch (error) {
            throw new NotFoundException([
                {
                    field: 'homeId',
                    message: 'Err-021',
                    detail: ERROR_MESSAGES['Err-021']
                },
            ]);
        }
    }

    async createHome(homeData: HomeDto): Promise<Home> {
        const newHome = this.homeRepository.create({ ...homeData });
        return this.homeRepository.save(newHome);
    }

    async updateHomeById(homeId: string, updateHomeInfo: Partial<Home>) {
        const result = await this.homeRepository.update({ homeId },
            { ...updateHomeInfo, price: updateHomeInfo.price?.toString() });
        if (result.affected === 0) {
            throw new NotFoundException([
                {
                    field: 'homeId',
                    message: 'Err-020',
                    detail: ERROR_MESSAGES['Err-020']
                },
            ]);
        }
    }

    async deleteHomeById(homeId: string): Promise<Home | null> {
        const home = await this.getHomeById(homeId);
        if (!home) {
            throw new NotFoundException([
                {
                    field: 'homeId',
                    message: 'Err-017',
                    detail: ERROR_MESSAGES['Err-017']
                },
            ]);
        }
        return home;
    }

    async getMostViewedHomes(limit: number = 10): Promise<Home[]> {
        const homes = await this.homeRepository
            .createQueryBuilder('home')
            .leftJoinAndSelect('home.homeType', 'homeType')
            .leftJoinAndSelect('home.address', 'address')
            .leftJoinAndSelect('address.ward', 'ward')
            .leftJoinAndSelect('ward.district', 'district')
            .where('home.homeStatus != :status', { status: 'UNAVAILABLE' })
            .orderBy('home.views', 'DESC')
            .limit(limit)
            .select([
                'home.homeId'
                , 'home.price'
                , 'home.views'
                , 'home.squareFeet'
                , 'home.homeName'
                , 'homeType.homeTypeName'
                , 'address.addressId'
                , 'address.street'
                , 'ward.wardId'
                , 'ward.wardName'
                , 'district.districtId'
                , 'district.districtName'
            ])
            .getMany();
        if (!homes || homes.length === 0) {
            throw new NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-022',
                    detail: ERROR_MESSAGES['Err-022'],
                },
            ]);
        }
        return homes;
    }

    async getLastAddedHomes(limit: number = 10): Promise<Home[]> {
        const homes = await this.homeRepository
            .createQueryBuilder('home')
            .leftJoinAndSelect('home.homeType', 'homeType')
            .leftJoinAndSelect('home.address', 'address')
            .leftJoinAndSelect('address.ward', 'ward')
            .leftJoinAndSelect('ward.district', 'district')
            .where('home.homeStatus != :status', { status: 'UNAVAILABLE' })
            .orderBy('home.createdAt', 'DESC')
            .limit(limit)
            .select([
                'home.homeId'
                , 'home.price'
                , 'home.views'
                , 'home.squareFeet'
                , 'home.homeName'
                , 'home.createdAt'
                , 'homeType.homeTypeName'
                , 'address.addressId'
                , 'address.street'
                , 'ward.wardId'
                , 'ward.wardName'
                , 'district.districtId'
                , 'district.districtName'
            ])
            .getMany();
        if (!homes || homes.length === 0) {
            throw new NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-022',
                    detail: ERROR_MESSAGES['Err-022'],
                },
            ]);
        }
        return homes;
    }

    async getRecommendedHomes(limit: number = 10): Promise<Home[]> {
        const homes = await this.homeRepository
            .createQueryBuilder('home')
            .leftJoinAndSelect('home.homeType', 'homeType')
            .leftJoinAndSelect('home.address', 'address')
            .leftJoinAndSelect('address.ward', 'ward')
            .leftJoinAndSelect('ward.district', 'district')
            .where('home.homeStatus != :status', { status: 'UNAVAILABLE' })
            .orderBy('home.priority', 'DESC')
            .limit(limit)
            .select([
                'home.homeId'
                , 'home.price'
                , 'home.views'
                , 'home.squareFeet'
                , 'home.homeName'
                , 'home.priority'
                , 'homeType.homeTypeName'
                , 'address.addressId'
                , 'address.street'
                , 'ward.wardId'
                , 'ward.wardName'
                , 'district.districtId'
                , 'district.districtName'
            ])
            .getMany();
        if (!homes || homes.length === 0) {
            throw new NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-022',
                    detail: ERROR_MESSAGES['Err-022'],
                },
            ]);
        }
        return homes;
    }

    async getHostPropertiesHome(limit: number = 10): Promise<Home[]> {
        // Lấy danh sách homeId được wishlist nhiều nhất
        const raw = await this.dataSource
            .getRepository('wish_list')
            .createQueryBuilder('w')
            .select('w.home_id', 'home_id')
            .addSelect('COUNT(*)', 'wish_count')
            .groupBy('w.home_id')
            .orderBy('wish_count', 'DESC')
            .getRawMany();
        const homeIds = raw.map(r => r.home_id);
        if (!homeIds.length) return [];
        const homes = await this.homeRepository
            .createQueryBuilder('home')
            .leftJoinAndSelect('home.homeType', 'homeType')
            .leftJoinAndSelect('home.address', 'address')
            .leftJoinAndSelect('address.ward', 'ward')
            .leftJoinAndSelect('ward.district', 'district')
            .where('home.homeId IN (:...homeIds)', { homeIds })
            .andWhere('home.homeStatus != :status', { status: 'UNAVAILABLE' })
            .orderBy('home.priority', 'DESC')
            .limit(limit)
            .select([
                'home.homeId',
                'home.price',
                'home.views',
                'home.squareFeet',
                'home.homeName',
                'home.priority',
                'homeType.homeTypeName',
                'address.addressId',
                'address.street',
                'ward.wardId',
                'ward.wardName',
                'district.districtId',
                'district.districtName',
            ])
            .getMany();
        return homes;
    }

    async getListHomeOrderBy(filter: keyof Home, limit: number = 10): Promise<Home[]> {
        try {
            const homes = await this.homeRepository.find({
                where: { homeStatus: Not('UNAVAILABLE') },
                order: { [filter]: 'DESC' },
                take: limit,
                relations: ['homeType', 'address'],
            });
            for (const h of homes) {
                h.address = await this.addressService.getAddressById(h.address.addressId);
            }
            return homes;
        } catch (error) {
            throw new NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-021',
                    detail: ERROR_MESSAGES['Err-021']
                },
            ]);
        }
    }

    async searchByFilter(
        filters: FilterPageDto
        , pageSize: number = 1
    ) {
        const page = filters.pageNumber ?? 1;
        const homesQuery = this.homeRepository
            .createQueryBuilder('home')
            .leftJoinAndSelect('home.amenities', 'amenity')
            .leftJoinAndSelect('home.homeType', 'homeType')
            .leftJoinAndSelect('home.address', 'address')
            .leftJoinAndSelect('address.ward', 'ward')
            .leftJoinAndSelect('ward.district', 'district')
            .leftJoinAndSelect('district.city', 'city')
            .leftJoin('city.country', 'country')
            .skip((page - 1) * pageSize)
            .take(pageSize)
        if (filters.homeTypeId) {
            homesQuery.andWhere('home.home_type_id = :homeTypeId', { homeTypeId: filters.homeTypeId });
        }
        if (filters.amenityId) {
            homesQuery.andWhere('amenity.amenity_id = :amenityId', { amenityId: filters.amenityId });
        }
        if (filters.wardId !== undefined) {
            homesQuery.andWhere('ward.ward_id = :wardId', { wardId: filters.wardId });
        }
        if (filters.districtId !== undefined) {
            homesQuery.andWhere('district.district_id = :districtId', { districtId: filters.districtId });
        }
        if (filters.cityId !== undefined) {
            homesQuery.andWhere('city.city_id = :cityId', { cityId: filters.cityId });
        }
        if (filters.countryId !== undefined) {
            homesQuery.andWhere('country.country_id = :countryId', { countryId: filters.countryId });
        }
        if (filters.priceMin !== undefined && filters.priceMax !== undefined) {
            homesQuery.andWhere('home.price BETWEEN :priceMin AND :priceMax', {
                priceMin: filters.priceMin,
                priceMax: filters.priceMax,
            });
        }
        if (filters.squareFeetMin !== undefined && filters.squareFeetMax !== undefined) {
            homesQuery.andWhere('home.squareFeet BETWEEN :squareFeetMin AND :squareFeetMax', {
                squareFeetMin: filters.squareFeetMin,
                squareFeetMax: filters.squareFeetMax,
            });
        }
        if (filters.bathroomMin !== undefined && filters.bathroomMax !== undefined) {
            homesQuery.andWhere('home.bathroom BETWEEN :bathroomMin AND :bathroomMax', {
                bathroomMin: filters.bathroomMin,
                bathroomMax: filters.bathroomMax,
            });
        }
        if (filters.bedroomMin !== undefined && filters.bedroomMax !== undefined) {
            homesQuery.andWhere('home.bedroom BETWEEN :bedroomMin AND :bedroomMax', {
                bedroomMin: filters.bedroomMin,
                bedroomMax: filters.bedroomMax,
            });
        }
        if (filters.searchKeyword && filters.searchKeyword.trim() !== '') {
            const keywords = filters.searchKeyword
                .split(',')
                .map(kw => kw.trim())
                .filter(kw => kw !== '');

            keywords.forEach((kw, index) => {
                homesQuery.andWhere(
                    `(home.home_name ILIKE :kwName${index} OR home.home_description ILIKE :kwDesc${index})`,
                    {
                        [`kwName${index}`]: `%${kw}%`,
                        [`kwDesc${index}`]: `%${kw}%`,
                    }
                );
            });
        }
        homesQuery.andWhere('home.home_status != :status', { status: 'UNAVAILABLE' });
        const homes = await homesQuery.getMany();
        if (!homes || homes.length === 0) {
            throw new NotFoundException([
                {
                    field: 'filter',
                    message: 'Err-022',
                    detail: ERROR_MESSAGES['Err-022'],
                },
            ]);
        }
        const result = homes.map(home => ({
            homeId: home.homeId,
            homeName: home.homeName,
            price: home.price,
            squareFeet: home.squareFeet,
            homeType: {
                homeTypeName: home.homeType?.homeTypeName || null,
            },
            amenities: home.amenities?.map(a => ({
                amenityName: a.amenityName,
            })) || [],
            homeDescription: home.homeDescription,
            districtName: home.address?.ward?.district?.districtName || null,
            cityName: home.address?.ward?.district?.city?.cityName || null,
        }));
        return result;
    }
}
