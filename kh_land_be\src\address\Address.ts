import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Wards } from "./wards/Wards";
import { Home } from "../home/<USER>";
import { News } from "../news/News";

@Index("idx_address_ward_id", ["wardId"], {})
@Entity("address", { schema: "public" })
export class Address {
  @Column("uuid", {
    primary: true,
    name: "address_id",
    default: () => "gen_random_uuid()",
  })
  addressId!: string;

  @Column("numeric", { name: "latitude", nullable: true })
  latitude!: string | null;

  @Column("numeric", { name: "longitude", nullable: true })
  longitude!: string | null;

  @Column("character varying", { name: "street", nullable: true })
  street!: string | null;

  @Column("integer", { name: "ward_id", nullable: true })
  wardId!: number | null;

  @ManyToOne(() => Wards, (wards) => wards.addresses, {
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  })
  @JoinColumn([{ name: "ward_id", referencedColumnName: "wardId" }])
  ward!: Wards;

  @OneToMany(() => Home, (home) => home.address)
  homes!: Home[];

  @OneToMany(() => News, (news) => news.address)
  news!: News[];
}
