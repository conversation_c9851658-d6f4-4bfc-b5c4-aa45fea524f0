import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Districts } from "../Districts";
import { Countries } from "./countries/Countries";

@Index("unique_city_name_country", ["countryId", "cityName"], { unique: true })
@Index("idx_cities_country_id", ["countryId"], {})
@Entity("cities", { schema: "public" })
export class Cities {
  @PrimaryGeneratedColumn({ type: "integer", name: "city_id" })
  cityId!: number;

  @Column("character varying", { name: "city_name" })
  cityName!: string;

  @Column("integer", { name: "country_id" })
  countryId!: number;

  @ManyToOne(() => Countries, (countries) => countries.cities, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
  })
  @JoinColumn([{ name: "country_id", referencedColumnName: "countryId" }])
  country!: Countries;

  @OneToMany(() => Districts, (districts) => districts.city)
  districts!: Districts[];
}
