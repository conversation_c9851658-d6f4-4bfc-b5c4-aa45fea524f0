import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Cities } from "./cities/Cities";
import { Wards } from "../Wards";

@Index("unique_district_name_city", ["cityId", "districtName"], { unique: true })
@Index("idx_districts_city_id", ["cityId"], {})
@Entity("districts", { schema: "public" })
export class Districts {
  @PrimaryGeneratedColumn({ type: "integer", name: "district_id" })
  districtId!: number;

  @Column("character varying", { name: "district_name" })
  districtName!: string;

  @Column("integer", { name: "city_id" })
  cityId!: number;

  @ManyToOne(() => Cities, (cities) => cities.districts, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
  })
  @JoinColumn([{ name: "city_id", referencedColumnName: "cityId" }])
  city!: Cities;

  @OneToMany(() => Wards, (wards) => wards.district)
  wards!: Wards[];
}
