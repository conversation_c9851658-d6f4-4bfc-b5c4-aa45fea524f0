{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/authentication/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAAoD;AACpD,+CAAoD;AACpD,2CAA4C;AAC5C,6DAAyD;AACzD,2CAA+C;AAGxC,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACmB,WAAyB,EAC1C,aAA4B;QAE5B,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QACD,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,WAAW,EAAE,SAAS;SACvB,CAAC,CAAC;QAVc,gBAAW,GAAX,WAAW,CAAc;IAW5C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAuC;QAEpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAvBY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGqB,4BAAY;QAC3B,sBAAa;GAHnB,WAAW,CAuBvB"}