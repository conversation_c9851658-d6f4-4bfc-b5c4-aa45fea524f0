import React from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

export default function Section_1() {
 
  return (
    <section className="py-5 bg-white">
  <div className="container">
    <h2 className="text-center fw-bold mb-5">Tính năng nổi bật của chúng tôi</h2>
    <div className="row g-4">
      {[
        {
          title: 'Tìm kiếm thông minh',
          desc: '<PERSON><PERSON> dàng tìm kiếm bất động sản theo vị trí, gi<PERSON> cả, diện tích và nhiều tiêu chí khác.',
          icon: '/search.svg',
        },
        {
          title: 'Danh sách dự án',
          desc: 'C<PERSON><PERSON> nhật thông tin chi tiết về các dự án bất động sản mới và đang triển khai.',
          icon: '/building.svg',
        },
        {
          title: '<PERSON><PERSON> tích thị trường',
          desc: '<PERSON><PERSON> cấp dữ liệu và báo cáo chuyên sâu giúp bạn đưa ra quyết định đầu tư sáng suốt.',
          icon: '/ChartColumn.svg',
        },
        {
          title: 'Tin tức & Xu hướng',
          desc: 'Luôn cập nhật những tin tức mới nhất và xu hướng nổi bật của thị trường bất động sản.',
          icon: '/Newspaper.svg',
        },
        {
          title: 'Hỗ trợ pháp lý',
          desc: 'Tư vấn và hỗ trợ các vấn đề pháp lý liên quan đến mua bán, cho thuê bất động sản.',
          icon: './CircleCheckBi.svg',
        },
        {
          title: 'Quản lý tài khoản',
          desc: 'Dễ dàng theo dõi các bất động sản yêu thích, lịch sử tìm kiếm và thông tin cá nhân.',
          icon: '/User.svg',
        },
      ].map((feature, idx) => (
        <div className="col-md-4" key={idx}>
              <div className="card h-100 text-center border-0 shadow-sm">
                <div className="card-body">
                  <div className="mb-3">
                    <div className="mb-3 d-flex align-items-end justify-content-center" style={{ height: 64 }}>
                    <img
                      src={feature.icon} // ✔ dùng icon từ feature object
                      alt={feature.title}
                      width={40} // ✔ thêm width
                      height={40} // ✔ thêm height
                      style={{ objectFit: 'contain' }}
                    />
                    </div>
                  </div>
                  <h5 className="card-title fw-semibold">{feature.title}</h5>
                  <p className="card-text text-muted">{feature.desc}</p>
                </div>
              </div>
            </div>
      ))}
    </div>
  </div>
</section>
  );
}