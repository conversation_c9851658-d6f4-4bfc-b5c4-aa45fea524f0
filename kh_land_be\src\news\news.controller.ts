import { NewsService } from './news.service';
import { Controller, Get, Param } from "@nestjs/common";

@Controller('news')
export class NewsController {
    constructor(private readonly newsService: NewsService) { }

    @Get('latest')
    async getLatestNews() {
        return this.newsService.getLatestNews();
    }

    @Get('most-viewed')
    async getMostViewedNews() {
        return this.newsService.getMostViewedNews();
    }

    @Get('recommended')
    async getRecommendedNews() {
        return this.newsService.getRecommendedNews();
    }

    @Get(':newsId')
    async getNewsById(@Param('newsId') newsId: string) {
        return this.newsService.getNewsById(newsId);
    }
}